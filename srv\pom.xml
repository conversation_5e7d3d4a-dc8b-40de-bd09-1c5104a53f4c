<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>usage-measurement-parent</artifactId>
		<groupId>com.sap.ich</groupId>
		<version>${revision}</version>
	</parent>

	<artifactId>usage-measurement</artifactId>
	<packaging>jar</packaging>

	<name>usage-measurement</name>

	<!-- ACTUAL DEPENDENCIES -->
	<dependencies>
		<!-- CDS SPRING BOOT STARTER -->
		<dependency>
			<groupId>com.sap.cds</groupId>
			<artifactId>cds-starter-spring-boot</artifactId>
		</dependency>
		

		<dependency>
			<groupId>com.sap.cds</groupId>
			<artifactId>cds-starter-cloudfoundry</artifactId>
		</dependency>


		<!-- ODATA PROTOCOL ADAPTER -->
		<dependency>
			<groupId>com.sap.cds</groupId>
			<artifactId>cds-adapter-odata-v4</artifactId>
			<scope>runtime</scope>
		</dependency>

	
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
    		<groupId>org.apache.httpcomponents.client5</groupId>
    		<artifactId>httpclient5</artifactId>
			<version>5.4.4</version>
		</dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
            <version>1.78.1</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>5.11.4</version>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>4.5.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.xerial</groupId>
			<artifactId>sqlite-jdbc</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
    		<groupId>org.yaml</groupId>
    		<artifactId>snakeyaml</artifactId>
		</dependency>
		<dependency>
            <groupId>com.sap.cloud.sdk.testutil</groupId>
            <artifactId>testutil-core</artifactId>
            <version>3.78.0</version>
            <scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
    				<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		    <exclusions>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

		<dependency>
            <groupId>com.sap.cloud.sdk.cloudplatform</groupId>
            <artifactId>scp-cf</artifactId>
            <exclusions>
                <exclusion>  <!-- declare the exclusion here -->
                    <groupId>com.mikesamuel</groupId>
                    <artifactId>json-sanitizer</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.12.0</version>
		  </dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-bom</artifactId>
				<version>4.1.119.Final</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<!-- SPRING BOOT PLUGIN -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<configuration>
					<skip>false</skip>
				</configuration>
				<executions>
					<execution>
						<id>repackage</id>
						<goals>
							<goal>repackage</goal>
						</goals>
						<configuration>
							<classifier>exec</classifier>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- STEPS TO BUILD CDS MODEL AND GENERATE POJOs -->
			<plugin>
				<groupId>com.sap.cds</groupId>
				<artifactId>cds-maven-plugin</artifactId>
				<version>${cds.services.version}</version>
				<executions>
					<execution>
						<id>cds.clean</id>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>

					<execution>
						<id>cds.install-node</id>
						<goals>
							<goal>install-node</goal>
						</goals>
						<configuration>
							<skip>${cdsdk-global}</skip>
							<downloadUrl>${node.url}</downloadUrl>
							<nodeVersion>${node.version}</nodeVersion>
						</configuration>
					</execution>

					<execution>
						<id>cds.install-dependencies</id>
						<goals>
							<goal>npm</goal>
						</goals>
						<configuration>
							<arguments>install @sap/cds-dk@${cds.install-cdsdk.version} --no-save</arguments>
						</configuration>
					</execution>

					<execution>
						<id>cds.resolve</id>
						<goals>
							<goal>resolve</goal>
						</goals>
					</execution>
					
					<execution>
						<id>cds.build</id>
						<goals>
							<goal>cds</goal>
						</goals>
						<configuration>
							<commands>
								<command>build</command>
								<command>deploy --to sql --dry &gt;
									"${project.basedir}/src/main/resources/schema.sql"</command>
								<command>compile ${project.basedir} -s all -2 edmx-v2 -l all -o ${project.basedir}/src/main/resources/edmx/v2</command>
							</commands>
						</configuration>
					</execution>

					<execution>
						<id>cds.generate</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<basePackage>cds.gen</basePackage>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.10</version>
				<configuration>
					<excludes>
						<exclude>**/*cds/gen/**/*.class</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<id>default-prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>default-report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<!-- A profile that uses global installation of @sap/cds-dk -->
		<profile>
			<id>cdsdk-global</id>
			<properties>
				<cdsdk-global>true</cdsdk-global>
			</properties>
		</profile>
	</profiles>
</project>
