@Library(['piper-lib', 'piper-lib-os']) _
node {
    try {
        stage('Preparation') {
            deleteDir()
            def params = checkout scm
            setupPipelineEnvironment(script: this, scmInfo: params)
        }


        def branchName = env.<PERSON><PERSON><PERSON>_NAME
        def blackduckProjectName = ""
        def checkmarxProjectName = ""
        def checkmarxOneCredentialsId = ""
        def checkmarxApplicationName = ""
        def sonarProjectKey = ""
        def sonarProjectName = ""

 		if (branchName == 'develop') {
            blackduckProjectName = "SHC-REGCOLLABORATION"
            checkmarxProjectName = "portal-Usage-Measurement-Service"
            checkmarxOneCredentialsId = "ICH_tkn_CheckmarxOne"
            checkmarxApplicationName = "ICH_CHECKMARX_ONE"
            sonarProjectKey = "SAP_ICH-portal-Usage-Measurement-Service"
            sonarProjectName = "portal-Usage-Measurement-Service" 
		} else if (branchName == 'staging') {
            blackduckProjectName = "SHC-REGCOLLABORATION-Staging"
            checkmarxProjectName = "portal-Usage-Measurement-Service-Staging"
            checkmarxOneCredentialsId = "ICH_tkn_Checkmarx_Stage"
            checkmarxApplicationName = "ICH_CHECKMARX_ONE_STAGING"
            sonarProjectKey = "Staging_ICH-portal-Usage-Measurement-Service"
            sonarProjectName = "Staging_ICH-portal-Usage-Measurement-Service" 
		} else if (branchName == 'production') {
            blackduckProjectName = "SHC-REGCOLLABORATION-Production"
            checkmarxProjectName = "portal-Usage-Measurement-Service-Production"
            checkmarxOneCredentialsId = "ICH_tkn_Checkmarx_Production"
            checkmarxApplicationName = "ICH_CHECKMARX_ONE_PRODUCTION"
            sonarProjectKey = "Production_ICH-portal-Usage-Measurement-Service"
            sonarProjectName = "Production_ICH-portal-Usage-Measurement-Service" 
		}
 

 
        // BlackDuck Scan
        stage('BlackDuck Scan') {
            detectExecuteScan(
                script: this,
                detectTokenCredentialsId: "regcol2-blackduck-token",
                buildTool: "mta",
                projectName: blackduckProjectName,
                version: "portal-Usage-Measurement-Service",
                versioningModel: "full",
                codeLocation: "${blackduckProjectName}/portal-Usage-Measurement-Service",
                minScanInterval: 0,
                groups: ["CMO3PLPORTAL2002"],
                failOn: ["NONE"],
                useDetect9: true,
                npmDependencyTypesExcluded: ["DEV"],
                scanProperties: [
                    "--detect.project.codelocation.unmap=true",
                    "--blackduck.trust.cert=true",
                    "--blackduck.timeout=6000",
                    "--detect.report.timeout=14400",
                    "--blackduck.signature.scanner.memory=8192",
                    "--detect.blackduck.signature.scanner.paths = /",
                    "--detect.impact.analysis.enabled=true",
                    "--detect.risk.report.pdf=true",
                    "--detect.risk.report.pdf.path=blackduckArtifacts",
                    "--detect.project.version.distribution=SAAS",
                    "--logging.level.com.synopsys.integration=DEBUG",
                    "--detect.accuracy.required=NONE",
                    "--detect.detector.search.depth=2",
                ]
            )
        }
 
        // SonarQube Scan
        stage('SonarQube Scan') {
            sonarExecuteScan(
                script: this,
                sonarTokenCredentialsId: 'ICH_Sonar_I061035',
                serverUrl: 'https://sonar.tools.sap',
                projectKey: sonarProjectKey,
                projectName: sonarProjectName
              )
        }
 
		//npmExecuteLint:
		stage('npmExecuteLint Scan'){
		  npmExecuteLint(
		   script: this,
		   dockerImage: 'devxci/mbtci-java23-node22',
		   install: true,
		   outputFileName: 'UI-lint.xml'
		  )
		}
 
        stage('Post-Scan Actions') {
            // Upload or archive reports, send notifications, etc.
            echo 'All security scans completed successfully.'
        }
    } catch (Exception e) {
        echo "Pipeline failed: ${e.message}"
        currentBuild.result = 'FAILURE'
        throw e
    }
}
 
 
