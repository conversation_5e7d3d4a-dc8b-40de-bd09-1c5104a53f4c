# Scans setup: Blackduck
general:
  buildTool: 'mta'
  productiveBranch: 'develop|hyperspace'
  verbose: true
  gitHttpsCredentialsId: 'ICH_tkn_GitHub'
  vaultAppRoleTokenCredentialsId: 'vault-approle-role-id-2614-28280'
  vaultAppRoleSecretTokenCredentialsId: 'vault-approle-secret-id-accessor-2614-28280'
  vaultPath: 'piper/PIPELINE-GROUP-2614'
  vaultBasePath: 'piper/PIPELINE-GROUP-2614'
  vaultPipelineName: 'PIPELINE-28280'
  vaultServerUrl: 'https://vault.tools.sap'
  vaultNamespace: 'ies/hyperspace/pipelines'
stages:
  Central Build:
    hadolintExecute: false
  Acceptance:
    verbose: true
    cfApiEndpoint: https://api.cf.eu10-004.hana.ondemand.com
    cfOrg: ich-qa-new-org
    cfSpace: portal
    cfCredentialsId: 'ICH_pwd_Cloud_Platform'
    mtaDeployParameters: -f -version-rule ALL
    testServerUrl: www.google.com
    mtaExtensionDescriptor: 'mta-qa.mtaext'
  Security:
    detectExecuteScan: true  
    codeqlExecuteScan: true
  Confirm:
    manualConfirmation: false
    manualConfirmationTimeout: 0.001
steps:
  artifactPrepareVersion:
    gitHttpsCredentialVaultSecretName: 'GROUP-SECRETS/github'
    versioningType: 'cloud'
  influxWriteData:
    influxServer: jenkins
  mtaBuild:
    mtaBuildTool: cloudMbt
    extensions: 'mta-dev.mtaext'
  executeBuild:
    xMakeNovaCredentialsId: 'hyperspace-xmake-p2003178105'
  sonarExecuteScan: 
    sonarTokenCredentialsId: 'ICH_tkn_Sonar'
    serverUrl: 'https://sonar.tools.sap'
    #host: 'https://sonar.wdf.sap.corp'
    verbose: false
    projectKey: 'SAP_ICH-portal-Usage-Measurement-Service'
    projectName: 'portal-Usage-Measurement-Service'   
  sapCumulusUpload:
    pipelineId: 'ccdadbae-9074-4515-bdc4-50ed7213203d'
    cumulusFileCredentialsId: 'hyperspace-cumulusupload-2614'
  codeqlExecuteScan:
    dockerImage: 'piper.int.repositories.cloud.sap/piper/codeql:jdk17'
    uploadResults: true
    buildTool: "maven"
    querySuite: "java-security-extended.qls"
    buildCommand: "mvn compile"
  detectExecuteScan:
    dockerImage: 'maven:3.6.3-jdk-11'
    projectName: "SHC-REGCOLLABORATION"
    version: "portal-Usage-Measurement-Service"
    versioningModel: 'full'
    codeLocation: "SHC-REGCOLLABORATION/portal-Usage-Measurement-Service"
    minScanInterval: 0
    groups:
      - "REGCOLLABPORTALOD10"
    failOn:
      - NONE
    detectTokenCredentialsId: "regcol2-blackduck-token"
    useDetect8: true
    npmDependencyTypesExcluded:
      - DEV
      - test
    scanProperties:
      - '--detect.detector.search.depth=2'
      - '--detect.project.version.distribution=SAAS'
      - '--detect.risk.report.pdf=true'
      - '--detect.impact.analysis.enabled=true'
      - '--blackduck.signature.scanner.memory=4096'
      - '--detect.timeout=6000'
      - '--blackduck.trust.cert=true'
      - '--logging.level.com.synopsys.integration=DEBUG'
      - '--detect.maven.excluded.scopes=test'
      - '--detect.project.codelocation.unmap=true'
      - '--detect.accuracy.required=NONE'      
      - '--detect.blackduck.signature.scanner.individual.file.matching=SOURCE'
      - '--detect.blackduck.signature.scanner.snippet.matching=SNIPPET_MATCHING'

  whitesourceExecuteScan:
    dockerImage: devxci/mbtci-java17-node18
    verbose: true
    stashContent:
    - buildDescriptor
    - opensourceConfiguration
    - classFiles
    - tests
    failOnSevereVulnerabilities: false
    configFilePath: 'wss-unified-agent.config'
    projectName: "portal-Usage-Measurement-Service"
    excludes: ['**/target/**/*', '**/unit-tests/**/*', '**/integration-tests/**/*', '**/performance-tests/**/*', '**/src/test/**/*','**/node_modules/**', '**/package.json']
    whitesourceUserTokenCredentialsId: "hyperspace-mend-ich-cloud-secret"
    whitesourceProductName: "SHC - SAP Information Collaboration Hub for Life Science"
    whitesourceProductToken: "f76a37a0-7421-46d5-aa22-9f275e3bda31"
    whitesourceProductVersion: 'develop'


  influxWriteData:
    influxServer: jenkins
    
  testsPublishResults:
    junit:
      updateResults: true
      archive: true
    jacoco:
      active: true
      archive: true    
