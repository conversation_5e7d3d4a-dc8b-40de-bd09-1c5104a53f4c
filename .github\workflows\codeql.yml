name: "CodeQL Advanced"

on:
  push:
    branches:
      - 'master'
      - 'develop'
    paths:
      - '.github/workflows/codeql.yml'
      - 'api/**'
      - 'ui/**'
  pull_request:
    branches:
      - 'master'
      - 'develop'
    paths:
      - '.github/workflows/codeql.yml'
      - 'api/**'
      - 'ui/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: [self-hosted, solinas-medium]
    permissions:
      security-events: write
      packages: read
      actions: read
      contents: read

    strategy:
      fail-fast: false
      matrix:
        language: ['java-kotlin']

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Set up Java Version 17
        if: matrix.language == 'java-kotlin'
        uses: actions/setup-java@v1
        with:
          java-version: 17

      - name: Set up Maven
        if: matrix.language == 'java-kotlin'
        uses: stCarolas/setup-maven@v4.5
        with:
          maven-version: 3.9.7

      - name: Build api module
        if: matrix.language == 'java-kotlin'
        run: mvn install -DskipTests -X -e

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"
