package com.sap.ich.usagemeasurement.controller;

import java.util.Optional;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sap.ich.usagemeasurement.dto.UsageMeasurementDTO;
import com.sap.ich.usagemeasurement.service.CustomerUsageService;
import com.sap.ich.usagemeasurement.service.RecordUsageService;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Optional;

@RestController
@RequestMapping("api/v1")
public class UsageController {

    @Autowired
    CustomerUsageService customerUsageService;

    @Autowired
    RecordUsageService recordUsageService;

    @PostMapping("/sendscpcconnusage")
    public String sendScpcConnUsage() {
        return customerUsageService.sendScpcConnUsage();
    }

    @PostMapping("/sendregreportusage")
    public String sendregreportusage() {
        return customerUsageService.sendRegReportUsage();
    }

    @GetMapping("/getticonnusage")
    public String getTiConnUsage(@RequestParam("date") Optional<String> dateOpt) {
        return customerUsageService.getTiConnUsage(dateOpt);
    }

    @GetMapping("/getstametrics")
    public String getStaMetrics(@RequestParam("date") Optional<String> dateOpt) {
        return customerUsageService.getStaMetrics(dateOpt);
    }

    @GetMapping("/gettiserialmetrics")
    public String getTiSerialMetrics(@RequestBody Optional<String> entity) {
        return customerUsageService.getTISerialMetrics(entity);
    }

    @GetMapping("/getcompletekeymetrics")
    public String getCompleteKeyMetrics(@RequestBody Optional<String> entity) {
        return customerUsageService.getCompleteKeyMetrics(entity);
    }

    @PostMapping("/sendcustomerregcollab")
    public String sendCustomerRegCollab(@RequestParam("date") Optional<String> dateOpt) {
        return customerUsageService.sendCustomerRegConnUsage(dateOpt);
    }

    @GetMapping("/getcustomerregcollab")
    public String getCustomerRegCollab(@RequestParam("date") Optional<String> dateOpt) {
        return customerUsageService.getCustomerRegConnUsage(dateOpt);
    }

    @PostMapping("/sendcustomerusoption/{aggregationType}")
    public ResponseEntity<?> sendCustomerUsOption(@PathVariable(name = "aggregationType") String aggregationType,
    @RequestHeader(name="x-sap-job-id",required = false) Integer jobId,
    @RequestHeader(name="x-sap-job-schedule-id",required = false) String scheduleId,
    @RequestHeader(name="x-sap-job-run-id",required = false) String runId,
    @RequestHeader(name="x-job-mode",required = false, defaultValue="true") boolean jobMode
    ){
        if(jobMode) {
            String escapedAggregationType = StringEscapeUtils.escapeJava(aggregationType);
            customerUsageService.sendCustomerUSOptionUsageAsync(escapedAggregationType,jobId,scheduleId,runId);
            return new ResponseEntity<>("sendusage daily usoption sync initiated", HttpStatus.ACCEPTED);
        } else {
            String escapedAggregationType = StringEscapeUtils.escapeJava(aggregationType);
            String responseMessage = customerUsageService.sendCustomerUSOptionUsage(escapedAggregationType);
            return new ResponseEntity<>(responseMessage, HttpStatus.OK);
        }
    }

    @PostMapping("/retrysendcustomerusoption")
    public String retrySendCustomerUsOption(@RequestParam("date") String date) {
        return customerUsageService.retrySendCustomerUSOptionUsage(date);
    }

    @PostMapping("/sendbasepackusage")
    public String sendbasepackusage() {
        return customerUsageService.sendBasePackData();
    }

    @GetMapping("/getbasepackusage")
    public String getbasepackusage() {
        return customerUsageService.getBasePackData();
    }

    @PostMapping("/sendmmusage")
    public String sendMMUsage(@RequestBody Optional<String> entity) {
        return customerUsageService.sendMMUsage(entity);
    }
    
    @PostMapping("/sendActiveConnectionUsage")
    public String sendActiveConnectionUsage(@RequestBody Optional<String> entity) {
        return customerUsageService.sendActiveConnectionUsage(entity);
    }

    @PostMapping("/sendbisusage")
    public String sendBisUsage(@RequestBody Optional<String> entity) {
        return customerUsageService.sendBisUsage(entity);
    }

    @GetMapping("/getusuage")
    public String getUsuage() {
        return customerUsageService.getData(Optional.empty());
    }

    @GetMapping("/getfunctionusage")
    public String getFunctionUsage() {
        return customerUsageService.getData(Optional.of("functional$1"));
    }

    @PostMapping("/recordusage")
    public void recordUsage(@RequestBody UsageMeasurementDTO entity) {
        recordUsageService.recordMessage(entity);
    }

    @PostMapping("/sendmmeventusage")
    public String sendmmeventusage(@RequestBody Optional<String> entity) {
        return customerUsageService.sendMMEventUsage(entity);
    }

    @PostMapping("/aggregatedailyusage")
    public String aggregateDailyUsageAll(@RequestBody Optional<String> entity) {
        recordUsageService.aggregateDailyUsage(entity);
        return "Aggregated usage for all measuring objects";
    }

    @PostMapping("/aggregatedailyusage/{measuringObject}")
    public String aggregateDailyUsageByObject(@RequestBody Optional<String> entity,
            @PathVariable("measuringObject") String measuringObject) {
        String escapedMeasuringObject = StringEscapeUtils.escapeJava(measuringObject);

        if (recordUsageService.aggregateDailyUsagebyMeasuringObject(entity, escapedMeasuringObject)) {
            return "Aggregation successful for measuring object: " + escapedMeasuringObject;
        } else {
            return "Aggregation failed for measuring object: " + escapedMeasuringObject;
        }
    }

    // cleanupbisusage - later
    // every month end
    // clean the daily usage table older than a month
    // clean the snapshot usage older than a year
}
