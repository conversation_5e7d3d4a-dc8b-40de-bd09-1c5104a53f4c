<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.sap.ich</groupId>
	<artifactId>usage-measurement-parent</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>

	<name>usage-measurement parent</name>

	<properties>
		<!-- OUR VERSION -->
		<revision>2411.5.0</revision>

		<!-- DEPENDENCIES VERSION -->
		<jdk.version>17</jdk.version>
		<cds.services.version>3.9.1</cds.services.version>
		<spring.boot.version>3.4.5</spring.boot.version>
		<xsuaa.version>3.5.6</xsuaa.version>
		<cds.install-cdsdk.version>8.6.1</cds.install-cdsdk.version>
		<cloud.sdk.version>5.14.0</cloud.sdk.version>
		<node.version>v22.11.0</node.version>
		<node.url>https://nodejs.org/dist/</node.url>

		<!-- <cds.install-node.downloadUrl>https://nodejs.org/dist/</cds.install-node.downloadUrl> -->
	</properties>

	<modules>
		<module>srv</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
                		<groupId>org.springframework.boot</groupId>
                		<artifactId>spring-boot-starter-actuator</artifactId>
            			<version>3.0.8</version>
           		</dependency>
			<!-- CDS SERVICES -->
			<dependency>
				<groupId>com.sap.cds</groupId>
				<artifactId>cds-services-bom</artifactId>
				<version>${cds.services.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- SPRING BOOT -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring.boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
			<!-- CLOUD SDK -->
            <dependency>
				<groupId>com.sap.cloud.sdk</groupId>
				<artifactId>sdk-modules-bom</artifactId>
				<version>${cloud.sdk.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- XSUAA -->
			<dependency>
				<groupId>com.sap.cloud.security</groupId>
				<artifactId>java-bom</artifactId>
				<version>${xsuaa.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!-- JAVA VERSION -->
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.12.1</version>
				<configuration>
					<release>${jdk.version}</release>
				</configuration>
			</plugin>

			<!-- MAKE SPRING BOOT PLUGIN RUNNABLE FROM ROOT -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<!-- POM FLATTENING FOR CI FRIENDLY VERSIONS -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<version>1.3.0</version>
				<configuration>
					<updatePomFile>true</updatePomFile>
					<flattenMode>resolveCiFriendliesOnly</flattenMode>
				</configuration>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
					</execution>
					<execution>
						<id>flatten.clean</id>
						<phase>clean</phase>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!-- PROJECT STRUCTURE CHECKS -->
			<plugin>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>Project Structure Checks</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireMavenVersion>
									<version>3.5.0</version>
								</requireMavenVersion>
								<requireJavaVersion>
									<version>${jdk.version}</version>
								</requireJavaVersion>
								<reactorModuleConvergence />
							</rules>
							<fail>true</fail>
						</configuration>
					</execution>
				</executions>
			</plugin>		
		</plugins>
	</build>
	<reporting>
	<plugins>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-surefire-report-plugin</artifactId>
			<version>2.22.0</version>
		</plugin>
	</plugins>
</reporting>
</project>
