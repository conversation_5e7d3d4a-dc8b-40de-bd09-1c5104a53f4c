package com.sap.ich.usagemeasurement.service;

import com.sap.ich.usagemeasurement.configuration.VcapProperties;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.AbstractMap;
import java.util.AbstractMap.SimpleEntry;
import java.util.Optional;

@Service
public class UsageMeasurementAPIV2Service {
    
    
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final RestTemplate restTemplate;

    private final VcapProperties properties;

    UsageMeasurementAPIV2Service(RestTemplate restTemplate,VcapProperties properties) {
        this.restTemplate = restTemplate;
        this.properties = properties;
    }
    
    public AbstractMap.SimpleEntry<Integer, String>  sendUsageData(String payload, Optional<String> billingModeOpt) throws IOException {

        try {
            String meterUrl = createMeterURL(billingModeOpt);
            logger.info("sending data: " + meterUrl);
            SimpleEntry<Integer, String> response = invokeIngestionPost(meterUrl,payload);
            String responseStr = StringEscapeUtils.escapeJava(response.getValue());
            logger.info("Received response from " + meterUrl + ": " + responseStr);
            return response;
        } catch (HttpServerErrorException e) {
            logger.error("Error fetching data from meter API", e);
            return new AbstractMap.SimpleEntry<>(500,e.getMessage());
        }
    }

    
    public String createSyncURL(Optional<String> billingModeOpt) {
        String baseUrl = properties.getSyncRetrievalDomain();
        return generateSyncReterivalDomin(baseUrl, billingModeOpt);
    }
    

    public String generateMeterDomin(String baseUrl, Optional<String> billingModeOpt) {
        if (baseUrl == null || baseUrl.isEmpty()) {
            logger.error("Base URL is not configured properly.");
            throw new IllegalStateException("Base URL for sync retrieval domain is missing.");
        }
    
        String meteringAccount = properties.getMeteringAccount();
        String billingMode;
        billingMode = billingModeOpt.orElseGet(properties::getMeterBillingMode);

        if (meteringAccount == null || meteringAccount.isEmpty()) {
            logger.error("Metering account is not configured properly.");
            throw new IllegalStateException("Metering account is missing.");
        }
    
        logger.info("Metering Account: " + meteringAccount);

        try {
            String fullUrl = baseUrl+"/v2/accounts/"+meteringAccount+"/namespaces/metering/datastreams/"+billingMode+"/eventBatch";
            logger.info("Generated Metering Data URL: " + fullUrl);
            return fullUrl;
        } catch (Exception e) {
            logger.error("Error constructing the metering data URL", e);
            throw new IllegalStateException("Failed to construct metering data URL", e);
        }
    }

    public String generateSyncReterivalDomin(String baseUrl, Optional<String> billingModeOpt) {
        if (baseUrl == null || baseUrl.isEmpty()) {
            logger.error("Base URL is not configured properly.");
            throw new IllegalStateException("Base URL for sync retrieval domain is missing.");
        }
    
        String meteringAccount = properties.getMeteringAccount();
        String billingMode;
        billingMode = billingModeOpt.orElseGet(properties::getMeterBillingMode);

        if (meteringAccount == null || meteringAccount.isEmpty()) {
            logger.error("Metering account is not configured properly.");
            throw new IllegalStateException("Metering account is missing.");
        }
    
        logger.info("Metering Account: " + meteringAccount);

        try {
            String fullUrl = baseUrl+"/v2/accounts/"+meteringAccount+"/namespaces/metering/datastreams/"+billingMode+"/events";
            logger.info("Generated Sync Reterival Data URL: " + fullUrl);
            return fullUrl;
        } catch (Exception e) {
            logger.error("Error constructing the metering data URL", e);
            throw new IllegalStateException("Failed to construct metering data URL", e);
        }
    }

    public String createMeterURL(Optional<String> billingModeOpt) {
        String baseUrl = properties.getUsageIngestionDomain();
        return generateMeterDomin(baseUrl, billingModeOpt);
        
    }
    public String fetchUsageData(Optional<String> billingModeOpt) throws IOException {
        try {
            String meterUrl = createSyncURL(billingModeOpt);
            logger.info("Fetching data from SyncReterivalUrl URL: " + meterUrl);
            String response = invokeSyncReterival(meterUrl);
            logger.info("Received response from " + meterUrl + ": " + response);
            return response;
        } catch (Exception e) {
            logger.error("Error fetching data from meter API", e);
            throw new IOException("Failed to fetch usage data", e);
        }
    }
    
    
    private String invokeSyncReterival(String url) {
        return restTemplate.getForObject(url, String.class);
    }
    

    public AbstractMap.SimpleEntry<Integer, String> invokeIngestionPost(String url, String payload) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> entity = new HttpEntity<>(payload, headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        return new AbstractMap.SimpleEntry<>(response.getStatusCode().value(), response.getBody());
    }

}
