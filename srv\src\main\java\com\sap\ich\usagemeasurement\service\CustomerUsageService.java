package com.sap.ich.usagemeasurement.service;

import cds.gen.ich.usage.measurement.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sap.cds.Result;
import com.sap.cds.Row;
import com.sap.cds.ql.CQL;
import com.sap.cds.ql.Insert;
import com.sap.cds.ql.Select;
import com.sap.cds.services.persistence.PersistenceService;
import com.sap.ich.usagemeasurement.configuration.VcapProperties;
import com.sap.ich.usagemeasurement.service.CompleteKeyMetricsService.MetricsDto;
import com.sap.ich.usagemeasurement.service.StaMetricsService.StaCustomer;
import com.sap.ich.usagemeasurement.service.StaMetricsService.StaMetricsDto;

import org.springframework.scheduling.annotation.Async;

import com.sap.ich.usagemeasurement.service.TISerialService.MessageMonitoringServiceDTO;
import com.sap.ich.usagemeasurement.service.TISerialService.MessageMonitoringServiceDTO.SerialNumber;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Modifier;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.stream.Collectors;

import org.json.JSONObject;


@Service
public class CustomerUsageService {
    
    private static final String ICH_UM_CUSTOMER_SCPC_CONNECTION = "ICH_UM_CUSTOMER_SCPC_CONNECTION";

    private static final String ICH_UM_CUSTOMER_USOPTION = "ICH_UM_CUSTOMER_USOPTION";

    private static final String ICH_UM_CUSTOMER_USOPTION_OLD = "ICH_UM_CUSTOMER_USOPTION_OLD";

    private static final String ICH_UM_CUSTOMER_REG_COLL_CONNECTION = "ICH_UM_CUSTOMER_REG_COLL_CONNECTION";

    private static final String ICH_UM_CUSTOMER_TI_CONNECTION = "ICH_UM_CUSTOMER_TI_CONNECTION";

    private static final String ICH_UM_CUSTOMER_STA_CONNECTION = "ICH_UM_CUSTOMER_STA_CONNECTION";

    private static final String ICH_UM_CUSTOMER_TI_SER_COUNT = "ICH_UM_CUSTOMER_TI_SER_COUNT";

    private static final String ICH_UM_CUSTOMER_VRS_SER_COUNT = "ICH_UM_CUSTOMER_VRS_SER_COUNT";

    private static final String ICH_UM_CUSTOMER_REG_REP_CONNECTION = "ICH_UM_CUSTOMER_REG_REP_CONNECTION";

    private static final String ICH_UM_CUSTOMER_CONNECTION = "ICH_UM_CUSTOMER_CONNECTION";

    private static final String ICH_UM_ACTIVE_USERS ="ICH_UM_ACTIVE_USERS";

    private static final String ICH_UM_CUSTOMER_MESSAGE = "ICH_UM_CUSTOMER_MESSAGE";

    private static final String ICH_UM_BIS_USAGE = "ICH_UM_BIS_ACCESS";

    private static final String ICH_MM_EVENT_USAGE = "ICH_MM_EVENTS";
    

    private final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Autowired
    private UsageMeasurementAPIV2Service usageMeasurementAPIService;

    @Autowired
    private PersistenceService db;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private StaMetricsService staMetricsService;

    @Autowired
    private CompleteKeyMetricsService completeKeyMetricsService;

    @Autowired
    private TISerialService tiSerialService;

    @Autowired
    private UsageRunLogService runLogService;

    @Autowired
    private JobSchedulerSerivce jobScheduler;

    @Autowired
    private VcapProperties properties;


    private String sendUsage(String measurementObjectId, String runlogUid, List<Record> records, Optional<String> billingModeOpt) {
        Gson gson = new GsonBuilder() 
                    .excludeFieldsWithModifiers(Modifier.TRANSIENT) 
                    .create();
        String payload = gson.toJson(records);
        if(null == payload) {
            return "There are no data to send to the Unified Metering ";
        }
        logger.info("The payload is");
        logger.info(payload);

        try{
            SimpleEntry<Integer, String> response = usageMeasurementAPIService.sendUsageData(payload, billingModeOpt);
            if (response.getKey().equals(201)) {
                runLogService.createSuccessEntry(runlogUid, response.getValue(),measurementObjectId);
            } else {
                runLogService.createServerErrorEntry(runlogUid,response.getValue(),measurementObjectId);
            }
        } catch(IOException ex) {
            logger.error("Failed to send message "+ ex.getMessage());
            return "Failed to send message";
        }
        return payload;
    }

    private String getRunlogUid() {
        return UUID.randomUUID().toString();
    }
    
    private String sendMeasurementUsage(String measurementObjectId) {
        String runlogUid = getRunlogUid();
        List<Record> records = getActiveCustomerRecords(measurementObjectId, runlogUid, Optional.empty());
        return sendUsage(measurementObjectId, runlogUid, records, Optional.empty());
    }

    /**
     * This method used to to simulate the output without sending to the UM
     * @param measurementObjectId
     * @return payload send to unified metering
     */
    private String getMeasurementUsage(String measurementObjectId, Optional<String> entityOpt) {
        String runlogUid = getRunlogUid();
        List<Record> records = getActiveCustomerRecords(measurementObjectId, runlogUid, entityOpt);
        Gson gson = new GsonBuilder() 
                    .excludeFieldsWithModifiers(Modifier.TRANSIENT) 
                    .create();
        return gson.toJson(records);
    }

    private String sendMeasurementUsage(String measurementObjectId, Optional<String> billingModeOpt, Optional<String> entityOpt) {
        String runlogUid = getRunlogUid();
        List<Record> records = getActiveCustomerRecords(measurementObjectId, runlogUid, entityOpt);
        return sendUsage(measurementObjectId, runlogUid, records, billingModeOpt);
    }
    
    public String sendScpcConnUsage() {
        return sendMeasurementUsage(ICH_UM_CUSTOMER_SCPC_CONNECTION);
    }

    public String sendCustomerUSOptionUsage(String aggregationType) {
        try {
            switch (aggregationType) {
                case "MO":
                    return sendMeasurementUsage(ICH_UM_CUSTOMER_USOPTION_OLD);
                case "DA":
                    LocalDate yesterday = LocalDate.now().minusDays(1);
                    Map<String, String> dateMap = new HashMap<>();
                    dateMap.put("startDate", yesterday.toString());
                    dateMap.put("endDate", yesterday.toString());
                    ObjectMapper objectMapper = new ObjectMapper();
                    String jsonString = objectMapper.writeValueAsString(dateMap);
                    return sendMeasurementUsage(ICH_UM_CUSTOMER_USOPTION, Optional.empty(), Optional.of(jsonString));
                default:
                    throw new IllegalArgumentException("measuringObject is not valid");
            }
        } catch (Exception ex) {
            logger.error("Failed to send message "+ ex.getMessage());
            return null;
        }
    }

    @Async
    public void sendCustomerUSOptionUsageAsync(String aggregationType, Integer jobId, String scheduleId, String runId) {
        boolean runLogStatus = true;
        String runLogMessage = null;
        try {
            runLogMessage = sendCustomerUSOptionUsage(aggregationType);
        } catch (Exception e) { //NOSONAR
            logger.error("sendCustomerUSOptionUsageAsync Failed to send message",e);
            runLogStatus = false;
            runLogMessage = "Customer USOption Usage service failed";
        }
        JSONObject jsonbody = new JSONObject();
        jsonbody.put("success", runLogStatus);
        jsonbody.put("message", runLogMessage);
        //String jobMessage = "{\"success\":" + runLogStatus + ", \"message\":\"" + runLogMessage + "\"}";
        jobScheduler.updateJobStatus(jobId, scheduleId, runId, jsonbody.toString());        
    }

    public String retrySendCustomerUSOptionUsage(String date) {
        LocalDate retryDate = LocalDate.parse(date);
        Map<String, String> dateMap = new HashMap<>();
        dateMap.put("startDate", retryDate.toString());
        dateMap.put("endDate", retryDate.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String jsonString = objectMapper.writeValueAsString(dateMap);
            return sendMeasurementUsage(ICH_UM_CUSTOMER_USOPTION, Optional.empty(), Optional.of(jsonString));
        } catch (JsonProcessingException e) {
            logger.error("Failed to send message while retry "+ e.getMessage());
            return null;
        }
    }

    public String sendCustomerRegConnUsage(Optional<String> dateOpt) {
            return sendMeasurementUsage(ICH_UM_CUSTOMER_REG_COLL_CONNECTION, Optional.empty(), dateOpt);
    }

    public String getCustomerRegConnUsage(Optional<String> dateOpt) { // not send to UMR , only read
        return getMeasurementUsage(ICH_UM_CUSTOMER_REG_COLL_CONNECTION, dateOpt);
}
    
    public String getTiConnUsage(Optional<String> dateOpt) {  // not send to UMR , only read
        return getMeasurementUsage(ICH_UM_CUSTOMER_TI_CONNECTION, dateOpt);
    }
    
    public String getStaMetrics(Optional<String> dateOpt) {  // not send to UMR , only read
        return getMeasurementUsage(ICH_UM_CUSTOMER_STA_CONNECTION, dateOpt);
    }

    public String getTISerialMetrics(Optional<String> entityOpt) { // not send to UMR , only read
        return getMeasurementUsage(ICH_UM_CUSTOMER_TI_SER_COUNT, entityOpt);
    }
    
    public String getCompleteKeyMetrics(Optional<String> entityOpt) {  // not send to UMR , only read
        return getMeasurementUsage(ICH_UM_CUSTOMER_VRS_SER_COUNT, entityOpt);
    }
    
    public String sendRegReportUsage() {
        return sendMeasurementUsage(ICH_UM_CUSTOMER_REG_REP_CONNECTION);
    }
    
    public String sendBasePackData() {
        String runlogUid = getRunlogUid();
        List<Record> recordList = getRecords(ICH_UM_CUSTOMER_CONNECTION, runlogUid, Optional.empty());
        return sendUsage(ICH_UM_CUSTOMER_CONNECTION, runlogUid, recordList, Optional.empty());
    }

    public String getBasePackData() {
        String runlogUid = getRunlogUid();
        List<Record> recordList = getRecords(ICH_UM_CUSTOMER_CONNECTION, runlogUid, Optional.empty());
        Gson gson = new GsonBuilder() 
            .excludeFieldsWithModifiers(Modifier.TRANSIENT) 
            .create();
        return gson.toJson(recordList);
    }

    public String sendMMUsage(Optional<String> entity) {
        return sendMeasurementUsage(ICH_UM_CUSTOMER_MESSAGE, Optional.of("functional$1"), entity);
    }

    public String sendActiveConnectionUsage(Optional<String> entity) {
        String runlogUid = getRunlogUid();
        List<Record> recordList = getRecords(ICH_UM_ACTIVE_USERS, runlogUid, entity);
        return sendUsage(ICH_UM_ACTIVE_USERS, runlogUid, recordList, Optional.of("functional$1"));
    }

    

    // check the orgtype from snapshot table
    // if the orgtype customer and than aggregate for current month data stats group by tenantid
    // send the details to unified metering  functional$1
    public String sendBisUsage(Optional<String> entity) {
        String runlogUid = getRunlogUid();
        List<Record> recordList = getRecords(ICH_UM_BIS_USAGE, runlogUid, entity);
        return sendUsage(ICH_UM_BIS_USAGE, runlogUid, recordList, Optional.of("functional$1"));
    }

    public String sendMMEventUsage(Optional<String> entity) {
        String runlogUid = getRunlogUid();
        List<Record> recordList = getRecords(ICH_MM_EVENT_USAGE, runlogUid, entity);
        return sendUsage(ICH_MM_EVENT_USAGE, runlogUid, recordList, Optional.of("functional$1"));
    }

    public String getData(Optional<String> billingModeOpt) {
        try{
            return usageMeasurementAPIService.fetchUsageData(billingModeOpt);
        } catch(IOException ex) {
            logger.error("Failed to send message "+ ex.getMessage());
            return null;
        }
        
    }
    
    private List<Record> getScpcRecords(String measurementObjectId,String runlogUid) {
        Result result = db.run(Select.from(UserUsageConnection_.class));
        List<Record> recordList = new ArrayList<>();
        for (Row row : result.list()) {
            
            String customerid = String.valueOf(row.get("CUSTOMER_ID"));
            String tenantId = String.valueOf(row.get("TENANT_ID"));
            if(!"null".equals(tenantId)) {
                Long activeConnection = Long.valueOf(String.valueOf(row.get("TOTAL_ACTIVE_CONN")));
                Long deactiveConnection = Long.valueOf(String.valueOf(row.get("TOTAL_DISCONN_CURRENT_YEAR_CONN")));
                Long deletedConnection = Long.valueOf(String.valueOf(row.get("TOTAL_DELETED_CONNECTED_CONN")));
                Long totalConnection = activeConnection + deactiveConnection+deletedConnection;
                
                Record rcd = createRecord(customerid,tenantId,measurementObjectId,totalConnection);
                recordList.add(rcd);
                logger.info(String.format("getScpcRecords Meter usage %s added : %s, total connection: %s, deleted connection: %s, disconnected: %s, active: %s", measurementObjectId, customerid, totalConnection, deletedConnection, deactiveConnection, activeConnection));

            }
        }
        return recordList;
    }


    private List<Record> getRegReportingRecords(String measurementObjectId,String runlogUid) {
        Result result = db.run(Select.from(UserUsageSubScenario_.class));
        List<Record> recordList = new ArrayList<>();
        for (Row row : result.list()) {
            String customerid = String.valueOf(row.get("CUSTOMER_ID"));
            String tenantId = String.valueOf(row.get("TENANT_ID"));
            if(!"null".equals(tenantId)) {
                Long totalSubScenario = Long.valueOf(String.valueOf(row.get("TOTAL_SUBCRIBED_SCENARIO")));
                Long totalDisconScenario = Long.valueOf(String.valueOf(row.get("TOTAL_DISCONN_CURRENT_YEAR")));
                Long totalConnection = totalSubScenario + totalDisconScenario;
                Record rcd = createRecord(customerid,tenantId,measurementObjectId,totalConnection);
                recordList.add(rcd);
                logger.info(String.format("getRegReportingRecords Meter usage %s added : %s, total connection: %s, sub scenario: %s, unsubbed scenario: %s", measurementObjectId, customerid, totalConnection, totalSubScenario, totalDisconScenario));

            }
        }
        return recordList;
        }

    private List<Record> getTitsRecords(String measurementObjectId, String runlogUid, Optional<String> dateOpt) {
        List<Record> recordList = new ArrayList<>();
        List<Map<String, Object>> queryResults;

        String date = null;
        if (dateOpt.isPresent()) {
            date = dateOpt.get();
            if (!date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new IllegalArgumentException("Invalid date format. Expected format: YYYY-MM-DD");
            }
            String sql = "SELECT * FROM F_TITS_CONNECTIONS_DATE(?)";
            queryResults = jdbcTemplate.queryForList(sql, date);
        } else {
            queryResults = db.run(Select.from(TitsUsageConnection_.class))
                     .list()
                     .stream()
                     .map(row -> (Map<String, Object>) row)
                     .toList();
        }

        for (Map<String, Object> row : queryResults) {
            String customerId = String.valueOf(row.get("CUSTOMER_ID"));
            String tenantId = String.valueOf(row.get("TENANT_ID"));
            if (!"null".equals(tenantId)) {
                Long totalActiveConn = Long.valueOf(String.valueOf(row.get("TOTAL_ACTIVE_CONN")));
                Long totalDisconnCurrentYearConn = Long.valueOf(String.valueOf(row.get("TOTAL_DISCONN_CURRENT_YEAR_CONN")));
                Long totalConnection = totalActiveConn + totalDisconnCurrentYearConn;
                Record titsRecord;
                if (date != null) {
                    titsRecord = createRecord(customerId, tenantId, measurementObjectId, totalConnection, dateToTimestampUtil(date), "MO");
                    logger.info(String.format("getTitsRecords Meter usage %s added: %s, total connection: %s, active conn: %s, disconnect: %s, date: %s",
                        measurementObjectId, customerId, totalConnection, totalActiveConn, totalDisconnCurrentYearConn, date));
                } else {
                    titsRecord = createRecord(customerId, tenantId, measurementObjectId, totalConnection);
                    logger.info(String.format("getTitsRecords Meter usage %s added: %s, total connection: %s, active conn: %s, disconnect: %s",
                        measurementObjectId, customerId, totalConnection, totalActiveConn, totalDisconnCurrentYearConn));

                }
                recordList.add(titsRecord);
            }
        }

        return recordList;
    }

    private List<Record> getStaRecords(String measurementObjectId, String runlogUid, Optional<String> dateOpt) {
        String date = dateOpt.isPresent() ? dateOpt.get() : null;
        StaMetricsDto staCount = staMetricsService.getStaCount(date);
        if (staCount == null || staCount.getStaCustomer() == null) {
            throw new IllegalStateException("sta metrics count is empty");
        }
        List<Record> recordList = new ArrayList<>();
        for (StaCustomer staCustomer : staCount.getStaCustomer()) {
            
            String customerid = String.valueOf(staCustomer.getSenderId());
            Result orgResult = db.run(Select.from(Organization_.class)
                                .where(x -> x.ID().eq(customerid)));
            Organization org = orgResult.first(Organization.class).orElse(null);
            if(org != null && org.getTenantId() != null) {
                String tenantId = org.getTenantId();
                Long dropShipCount = staCustomer.getDropShipCount() !=null ? staCustomer.getDropShipCount() : Long.valueOf(0);
                Record staRecord;
                if (date != null) {
                    logger.info(String.format("getStaRecords Meter usage %s added : %s, sold to count %s, drop ship count %s, date: %s", measurementObjectId, customerid, Long.valueOf(staCustomer.getSoldToCount()), dropShipCount, dateOpt.get()));
                    staRecord = createRecord(customerid,tenantId,measurementObjectId,Long.valueOf(staCustomer.getSoldToCount() + dropShipCount), dateToTimestampUtil(date), "MO");
                } else {
                    logger.info(String.format("getStaRecords Meter usage %s added : %s, sold to count %s, drop ship count %s", measurementObjectId, customerid, Long.valueOf(staCustomer.getSoldToCount()), dropShipCount));
                    staRecord = createRecord(customerid,tenantId,measurementObjectId,Long.valueOf(staCustomer.getSoldToCount() + dropShipCount));
                }
                recordList.add(staRecord);
            }
        }
        return recordList;
    }


    private List<Record> getTiSerialRecords(String measurementObjectId,String runlogUid, Optional<String> entityOpt) {
        LocalDate startDate = null;
        LocalDate endDate = null;
        MessageMonitoringServiceDTO serialCount;
        if (entityOpt.isPresent()) {
            List<LocalDate> dates = CustomerUsageService.parseStartAndEndDate(entityOpt);
            startDate = dates.get(0);
            endDate = dates.get(1);
        }
        if (startDate != null && endDate != null) {
            serialCount = tiSerialService.getSerialCount(startDate.toString(), endDate.toString());
        } else {
            serialCount = tiSerialService.getSerialCount();
        }

        if (serialCount == null || serialCount.getValue() == null) {
            throw new IllegalStateException("ti serial metrics count is empty");
        }
        List<Record> recordList = new ArrayList<>();

        for (SerialNumber serialNumber : serialCount.getValue()) {
            
            String customerid = String.valueOf(serialNumber.getPnid());
            Result orgResult = db.run(Select.from(Organization_.class)
                                .where(x -> x.ID().eq(customerid)));
            Organization org = orgResult.first(Organization.class).orElse(null);
            if(org != null && org.getTenantId() != null) {
                String tenantId = org.getTenantId();
                Record rcd;
                
                if (startDate != null && startDate.equals(endDate)) {
                    logger.info(String.format("getTiSerialRecords Meter usage %s added : %s, DA aggregation", measurementObjectId, customerid));
                    rcd = createRecord(customerid,tenantId,measurementObjectId,Long.valueOf(serialNumber.getCount()), dateToTimestampUtil(startDate),"DA");
                } else {
                    rcd = createRecord(customerid,tenantId,measurementObjectId,Long.valueOf(serialNumber.getCount()));
                }
                recordList.add(rcd);
                logger.info(String.format("getTiSerialRecords Meter usage %s added : %s, serial count %s, on date: start %s and end %s", measurementObjectId, customerid, Long.valueOf(serialNumber.getCount()), startDate,endDate));
            }
        }
        return recordList;
    }

    private List<Record> getCustomerMessageRecords(String measurementObjectId, String runlogUid, Optional<String> entityOpt) {
        List<LocalDate> dates = parseStartAndEndDate(entityOpt);
        LocalDate startDate = dates.get(0);
        LocalDate endDate = dates.get(1);


        MessageMonitoringServiceDTO messageCount = tiSerialService.getMessageCount(startDate, endDate);
        if (messageCount == null || messageCount.getValue() == null) {
            throw new IllegalStateException("message metrics count is empty");
        }

        Map<String, String> tenantIdMap = new HashMap<>();
        db.run(Select.from(Organization_.class).columns(x -> x.ID(), x -> x.tenantId())
            .where(x -> x.tenantId().isNotNull()))
            .forEach(row -> tenantIdMap.put(String.valueOf(row.get("ID")), String.valueOf(row.get("tenantId"))));

        List<Record> recordList = new ArrayList<>();
        for (SerialNumber serialNumber : messageCount.getValue()) {
            String customerId = serialNumber.getPnid();
            Long customerCount = Long.valueOf(serialNumber.getCount());
            if (tenantIdMap.containsKey(customerId)) {
                String tenantId = tenantIdMap.get(customerId);
                Record rcd = createRecord(customerId,tenantId,measurementObjectId,customerCount);
                recordList.add(rcd);
                logger.info(String.format("getCustomerMessageRecords Meter usage %s added : %s, message count %s", measurementObjectId, customerId, customerCount));
            }
            else {
                logger.info(String.format("getCustomerMessageRecords Meter usage %s skipped : %s, message count %s", measurementObjectId, customerId, customerCount));
            }
        }
        return recordList;
    }

    private List<Record> getActiveCustomerConnectionRecords(String measurementObjectId, String runlogUid, Optional<String> entityOpt){
        List<UsageRunLog> runLogList = new ArrayList<>();
        List<LocalDate> dates = parseStartAndEndDate(entityOpt);
        LocalDate endDate = dates.get(1);
        // Convert LocalDate to Instant
        Instant endDateInstant = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
    
        Result result = db.run(Select.from(UserActiveConnectionUsage_.class)
                        .columns(b -> b.CUSTOMER_ID(), b -> b.TENANT_ID(), b -> CQL.count(b.CUSTOMER_ID()).as("TOTAL_ACTIVE_CONN"))
                        .where(b -> b.MODIFIED_AT().lt(endDateInstant))
                        .groupBy(b -> b.CUSTOMER_ID(), b -> b.TENANT_ID()));
                                                   
        List<Record> recordList = new ArrayList<>();
        for (Row row : result.list()) {
            String customerid = String.valueOf(row.get("CUSTOMER_ID"));
            String tenantId = String.valueOf(row.get("TENANT_ID"));
            if(!"null".equals(tenantId)) {
                Long activeConnection = Long.valueOf(String.valueOf(row.get("TOTAL_ACTIVE_CONN"))); 
                Record rcd = createRecord(customerid,tenantId,measurementObjectId,activeConnection);
                recordList.add(rcd);
                logger.info(String.format("activeConnectionUsageRecords Meter usage %s added : %s, active: %s, tenantID : %s", measurementObjectId, customerid, activeConnection,tenantId));
                
            }
            //This will keep track of customer which were not sent to unifiied metering due to data error. will maintain transparency if data corrected later by OBT
            else{
                    logger.info(String.format("%s skipped :  %s",measurementObjectId,customerid));
                    UsageRunLog usageLogEntry = runLogService.getUsageRunLog(runlogUid, customerid,"DATA_ERROR","Tenantid is not available", measurementObjectId);
                    runLogList.add(usageLogEntry);
                
            }
        }
        runLogList.forEach(log -> db.run(Insert.into(UsageRunLog_.CDS_NAME).entry(log)));
        return aggregateRecords(measurementObjectId,recordList);
    }

    private List<Record> getBisUsageAggregatedRecords(String measurementObjectId, String runlogUid, Optional<String> entityOpt) {
        List<LocalDate> dates = parseStartAndEndDate(entityOpt);
        LocalDate startDate = dates.get(0);
        LocalDate finalEndDate = dates.get(1);
        
        List<Record> recordList = new ArrayList<>();
        db.run(Select.from(UsageDetailSnapshot_.class).columns(u -> u.pnid(), u -> u.tenantId(), u -> u.count())
            .where(u -> u.snapshotDate().between(startDate, finalEndDate).and(u.measuringObject().eq(measurementObjectId))))
            .list().forEach(u -> {
                String pnid = u.as(UsageDetailSnapshot.class).getPnid();
                String tenantId = u.as(UsageDetailSnapshot.class).getTenantId();
                Long totalUsage = Long.valueOf(u.as(UsageDetailSnapshot.class).getCount());
                Record bisUsageRecord = createRecord(pnid, tenantId, measurementObjectId, totalUsage);
                recordList.add(bisUsageRecord);
                logger.info(String.format("getBisUsageRecords Meter usage %s added : %s, total usage %s", measurementObjectId, u.get("pnid").toString(), totalUsage));
            });
        return aggregateRecords(measurementObjectId, recordList);
    }


    private List<Record> getMMEventAggregatedRecords(String measuringOjectId,String runlogUid, Optional<String> entityOpt) {
        List<LocalDate> dates = parseStartAndEndDate(entityOpt);
        LocalDate startDate = dates.get(0);
        LocalDate finalEndDate = dates.get(1);
        List<Record> aggregateRecords = new ArrayList<>();
        List<String> measurementObjectIds = Arrays.asList("ICH_MMSG_UI_RETRIGGER","ICH_MMSG_UI_ACCESS");
        List<Record> recordList = new ArrayList<>();
        db.run(Select.from(UsageDetailSnapshot_.class).columns(u -> u.pnid(), u -> u.tenantId(),u -> u.measuringObject(), u -> u.count())
            .where(u -> u.snapshotDate().between(startDate, finalEndDate).and(u.measuringObject().in(measurementObjectIds))))
            .list().forEach(u -> {
                String pnid = u.as(UsageDetailSnapshot.class).getPnid();
                String measuringObject = u.as(UsageDetailSnapshot.class).getMeasuringObject();
                String tenantId = u.as(UsageDetailSnapshot.class).getTenantId();
                Long totalUsage = Long.valueOf(u.as(UsageDetailSnapshot.class).getCount());
                Record mmEventUsageRecord = createRecord(pnid, tenantId, measuringObject, totalUsage);
                recordList.add(mmEventUsageRecord);
                logger.info(String.format("getMMEventAggregatedRecords Event %s Meter usage %s added : %s, total usage %s",measuringOjectId, measuringObject, u.get("pnid").toString(), totalUsage));
            });
        for (String measurementObject:measurementObjectIds) {
            aggregateRecords.addAll(aggregateRecords(measurementObject, recordList));
        }
        return aggregateRecords;
            
    }

    
    public static List<LocalDate> parseStartAndEndDate(Optional<String> entityOpt) {
        LocalDate startDate;
        LocalDate endDate;

        if (entityOpt.isPresent()) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode entityNode = mapper.readTree(entityOpt.get());
                if (entityNode.has("startDate")) {
                    startDate = LocalDate.parse(entityNode.get("startDate").asText());
                } else {
                    startDate = LocalDate.now().withDayOfMonth(1);
                }
                if (entityNode.has("endDate")) {
                    endDate = LocalDate.parse(entityNode.get("endDate").asText());
                } else {
                    endDate = LocalDate.now();
                }
            } catch (JsonProcessingException e) {
                throw new IllegalStateException("entity is not valid json");
            }
        } else {
            startDate = LocalDate.now().withDayOfMonth(1);
            endDate = LocalDate.now();
        }

        return new ArrayList<>(List.of(startDate, endDate));
    }

    /**
     *  Return true, if customer has mah role , and customer has mah and other roles than wholesaler
     * @param orgRoles
     * @return
     */
    private boolean hasMahNotWHRoles(List<OrganizationRoles> orgRoles) throws IllegalArgumentException { 
        boolean hasMahRole = false;
        boolean hasWhRole = false;
    
        for (OrganizationRoles role : orgRoles) {
            if (role.getRoleId().equals("1")) { // MAH
                hasMahRole = true; 
            }
            if (role.getRoleId().equals("2")) { // Wholesaler
                hasWhRole = true;
                break;
            }
        }
        return hasMahRole && !hasWhRole;
    }

    /**
     *  Case 1 : If Customer = MAH and uses SAP repository and VRS Count  >0 then Use VRS serial count. However if VRS Serial count is 0 then use TI Serial Count.
     *  Case 2:  If customer has the roles Wholesaler and MAH assigned, then connection count will be  sum of VRS Serial count + TI Serial Count 
     *  Case 3:  If customer has one role as MAH and other role is not Wholesaler then connection count will be the VRS Serial count only.
     */
    private List<Record> getCustomerUSOptionRecords(String customerUSOptionMOId,String runlogUid, Optional<String> entityOpt) {
        String tiSerialMoId = ICH_UM_CUSTOMER_TI_SER_COUNT;
        String completeKeyMoId = ICH_UM_CUSTOMER_VRS_SER_COUNT;
        List<Record> tiSerialRecords = getActiveCustomerRecords(tiSerialMoId, runlogUid, entityOpt);
        List<Record> completekeyRecords = getActiveCustomerRecords(completeKeyMoId, runlogUid, entityOpt);
        Map<String,Record> sumRecordsMap = new HashMap<>();
        for(Record rcd: completekeyRecords) {
            List<OrganizationRoles> orgRoles = db.run(Select.from(OrganizationRoles_.class)
                                    .where(x -> x.org_ID().eq(rcd.customerId))).listOf(OrganizationRoles.class);

            
            if(!(hasMahNotWHRoles(orgRoles) && rcd.measure.value > 0L)) { // include tiserial case 2 
                Optional<Record> tiRecordOpt = tiSerialRecords.stream()
                .filter(dto -> dto.customerId.equals(rcd.customerId))
                .findFirst();
            
                if (tiRecordOpt.isPresent()){
                    Record tiRecord = tiRecordOpt.get();
                    rcd.sumValue(tiRecord); 
                    logger.info(String.format("getCustomerUSOptionRecords  nothasMahRoleOnly %s added : count %s", rcd.getTenantId(),tiRecord.measure.getValue()));
                }
            }
            // other case 1 and case 3
            rcd.setMeasurementObjectId(customerUSOptionMOId);
            sumRecordsMap.put(rcd.getTenantId(),rcd);
            logger.info(String.format("getCustomerUSOptionRecords  hasMahRoleOnly %s added : count %s", rcd.getTenantId(),rcd.measure.getValue()));

        }

        // for remaining tiserial records 
        for(Record rcd: tiSerialRecords) {
            if(!sumRecordsMap.containsKey(rcd.getTenantId())) {
                rcd.setMeasurementObjectId(customerUSOptionMOId);
                sumRecordsMap.put(rcd.getTenantId(),rcd);
                logger.info(String.format("getCustomerUSOptionRecords  remaining %s added : count %s", rcd.getTenantId(),rcd.measure.getValue()));
            }
        }
        return new ArrayList<>(sumRecordsMap.values());
    }

    private List<Record> getCustomerRegCollabRecords(String measurementObjectId,String runlogUid, Optional<String> entityOpt) {
        String tiConnid = ICH_UM_CUSTOMER_TI_CONNECTION;
        String staConnId = ICH_UM_CUSTOMER_STA_CONNECTION;
        List<Record> titsRecords = getActiveCustomerRecords(tiConnid, runlogUid, entityOpt);
        List<Record> completekeyRecords = getActiveCustomerRecords(staConnId, runlogUid, entityOpt);
        Map<String,Record> sumRecordsMap = new HashMap<>();
        List<Record> allRecords = new ArrayList<>(titsRecords);
        allRecords.addAll(completekeyRecords);
        for(Record rcd: allRecords) {
            rcd.setMeasurementObjectId(measurementObjectId);
            sumRecordsMap.merge(rcd.getTenantId(), rcd,(existingRecord,newRecord) -> {
                existingRecord.sumValue(newRecord);
                return existingRecord;
            });
        }
        return new ArrayList<>(sumRecordsMap.values());
    }

    private List<Record> getActiveCustomerRecords(String measurementObjectId,String runlogUid, Optional<String> entityOpt) {
        List<UsageRunLog> runLogList = new ArrayList<>();
        Map<String,Record> recordsMap = new HashMap<>();
        List<Record> records= getRecords(measurementObjectId, runlogUid, entityOpt);
        List<Organization> activeCustomers = db.run(Select.from(Organization_.class)
                        .where(x -> x.type_ID().eq(1).and(x.status_ID().eq("1"))))
        .listOf(Organization.class);

        for (Organization activeCustomer : activeCustomers) {
            if(activeCustomer.getTenantId() == null) {
                logger.info(String.format("%s skipped :  %s",measurementObjectId,activeCustomer.getId()));
                UsageRunLog usageLogEntry = runLogService.getUsageRunLog(runlogUid, activeCustomer.getId(),"DATA_ERROR","Tenantid is not available", measurementObjectId);
                runLogList.add(usageLogEntry);
            } else {
                Optional<Record> recordOpt = records.stream()
                .filter(dto -> dto.customerId.equals(activeCustomer.getId()))
                .findFirst();        
                
                if (recordOpt.isPresent()){
                    recordsMap.put(activeCustomer.getId(),recordOpt.get());
                } else {  
                    LocalDate startDate = null;
                    LocalDate endDate = null;
                    Record rcd  = null;
                    if (entityOpt.filter(entity -> entity.trim().matches("^\\{.*\\}$")).isPresent()) {
                        List<LocalDate> dates = CustomerUsageService.parseStartAndEndDate(entityOpt);
                        startDate = dates.get(0);
                        endDate = dates.get(1);
                    }
                    if (startDate != null && startDate.equals(endDate)) {
                        logger.info("getActiveCustomerRecords Meter usage {} added : {}, DA aggregation", measurementObjectId, activeCustomer.getId());
                        rcd = createRecord(activeCustomer.getId(),activeCustomer.getTenantId(),measurementObjectId,0L, dateToTimestampUtil(startDate),"DA");
                    } else if (entityOpt.isPresent() && entityOpt.get().matches("\\d{4}-\\d{2}-\\d{2}")) {
                        rcd = createRecord(activeCustomer.getId(), activeCustomer.getTenantId(), measurementObjectId, 0L, dateToTimestampUtil(entityOpt.get()), "MO");
                    } else {
                        rcd = createRecord(activeCustomer.getId(), activeCustomer.getTenantId(), measurementObjectId, 0L);
                    }
                    recordsMap.put(activeCustomer.getId(),rcd);
                } 
            }
        }
        runLogList.forEach(log -> db.run(Insert.into(UsageRunLog_.CDS_NAME).entry(log)));
        return aggregateRecords(measurementObjectId, recordsMap.values());
    }

    private List<Record> getRecords(String measurementObjectId,String runlogUid, Optional<String> entityOpt) {
        switch (measurementObjectId) {
            case ICH_UM_CUSTOMER_TI_CONNECTION:
                return getTitsRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_STA_CONNECTION:
                return getStaRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_TI_SER_COUNT:
                return getTiSerialRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_VRS_SER_COUNT:
                return getCompleteKeyRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_SCPC_CONNECTION:
                return getScpcRecords(measurementObjectId, runlogUid);
            case ICH_UM_CUSTOMER_REG_REP_CONNECTION:
                return getRegReportingRecords(measurementObjectId, runlogUid);
            case ICH_UM_CUSTOMER_CONNECTION:
                return getBasePackRecords(measurementObjectId, runlogUid);
            case ICH_UM_CUSTOMER_REG_COLL_CONNECTION:
                return getCustomerRegCollabRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_USOPTION_OLD:
            case ICH_UM_CUSTOMER_USOPTION:
                return getCustomerUSOptionRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_CUSTOMER_MESSAGE:
                return getCustomerMessageRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_BIS_USAGE:
                return getBisUsageAggregatedRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_MM_EVENT_USAGE:
                return getMMEventAggregatedRecords(measurementObjectId, runlogUid, entityOpt);
            case ICH_UM_ACTIVE_USERS:
                return getActiveCustomerConnectionRecords(measurementObjectId, runlogUid, entityOpt);    
            default:
                throw new IllegalStateException("Measurement object not implemented "+measurementObjectId);
        }

    }

    private List<Record> aggregateRecords(String measurementObjectId, Collection<Record> records) {
        Map<String,Record> sumRecordsMap = new HashMap<>();
        if (measurementObjectId.equals(ICH_UM_CUSTOMER_CONNECTION)) {
            return new ArrayList<>(records);
        }
        else {
            for(Record rcd: records) {
                if(rcd.measure.id != null && rcd.measure.id.equals(measurementObjectId)) {
                    sumRecordsMap.merge(rcd.getTenantId(), rcd,(existingRecord,newRecord) -> {
                        existingRecord.sumValue(newRecord);
                        return existingRecord;
                    });
                }
                
            }
        }
        return new ArrayList<>(sumRecordsMap.values());
    }
    

    


    private List<Record> getCompleteKeyRecords(String measurementObjectId,String runlogUid, Optional<String> entityOpt) {
        LocalDate startDate = null;
        LocalDate endDate = null;
        MetricsDto meterics;
        if (entityOpt.isPresent()) {
            List<LocalDate> dates = CustomerUsageService.parseStartAndEndDate(entityOpt);
            startDate = dates.get(0);
            endDate = dates.get(1);
        }
        if (startDate != null && endDate != null) {
            meterics = completeKeyMetricsService.getTotalCount(startDate.toString(), endDate.toString());
        } else {
            meterics = completeKeyMetricsService.getTotalCount();
        }
        
        if (meterics == null || meterics.getTotalKeyCount() == null) {
            throw new IllegalStateException("sta metrics count is empty");
        }
        List<Record> recordList = new ArrayList<>();

        Map<String, Integer> totalKeyCountMap = meterics.getTotalKeyCount();
        for (String customerPnid : meterics.getTotalKeyCount().keySet()) {
            
            Result orgResult = db.run(Select.from(Organization_.class)
                                .where(x -> x.ID().eq(customerPnid)));
            Organization org = orgResult.first(Organization.class).orElse(null);
            if(org != null) {
                String tenantId = org.getTenantId();
                if(!"null".equals(tenantId)) {
                    Record rcd;
                    if (startDate != null && startDate.equals(endDate)) {
                        rcd = createRecord(customerPnid,tenantId,measurementObjectId,Long.valueOf(totalKeyCountMap.get(customerPnid)), dateToTimestampUtil(startDate),"DA");
                    } else {
                        rcd = createRecord(customerPnid,tenantId,measurementObjectId,Long.valueOf(totalKeyCountMap.get(customerPnid)));
                    }
                    recordList.add(rcd);
                    logger.info(String.format("getCompleteKeyRecords Meter usage %s added : %s,  serial count %s, on date %s", measurementObjectId, customerPnid, Long.valueOf(totalKeyCountMap.get(customerPnid)), startDate));
                }
            }
            
        }
        return recordList;
    }

    /**
     * 
     * if customer tenant having single organization : cus1 : basepack true , finalvalue : 1
     * if customer tenant having single organization : cus1 : basepack false , finalvalue : 1
     * if customer tenant having multiple organization : cus1 : basepack false , cust2: basepack true finalvalue : 2
     * if customer tenant having multiple organization : cus1 : basepack true , cust2: basepack true finalvalue : 2
     * if customer tenant having multiple organization : cus1 : basepack false , cust2: basepack true cust3 : basepack false finalvalue : 2
     * if customer tenant having multiple organization : cus1 : basepack true , cust2: basepack true  cust3 : basepack true finalvalue : 3

     * @param measurementObjectId
     * @param runlogUid
     * @return
     */
    private List<Record> getBasePackRecords(String measurementObjectId ,String runlogUid) {
        Result result = db.run(Select.from(BasePackCustomer_.class));
        List<Record> recordList = new ArrayList<>();

        Map<String, List<Row>> tenantGroups = result.list().stream()
            .filter(row -> !"null".equals(String.valueOf(row.get("TENANT_ID"))))
            .collect(Collectors.groupingBy(row -> String.valueOf(row.get("TENANT_ID"))));

        for (Map.Entry<String, List<Row>> entry : tenantGroups.entrySet()) {
            String tenantId = entry.getKey();
            List<Row> tenantRows = entry.getValue();
            
            Long totalConnections = tenantRows.stream()
                .mapToLong(row -> {
                    Long totalSub = Long.valueOf(String.valueOf(row.get("TOTAL_SUBCRIBED_SCENARIO")));
                    Long totalDiscon = Long.valueOf(String.valueOf(row.get("TOTAL_DISCONN_CURRENT_YEAR")));
                    return totalSub + totalDiscon;
                }).sum();

            Long finalValue = 0L;
            long additionalBasePackCount = 0;
            long nonAdditionalBasePackCount = 0;

            if (totalConnections > 0) {
                for (Row row : tenantRows) {
                    String customerId = String.valueOf(row.get("CUSTOMER_ID"));
                    boolean hasAdditionalBasePack = Boolean
                            .parseBoolean(String.valueOf(row.get("ADDITONAL_BASE_PACK")));

                    Integer nonReg = Integer.valueOf(String.valueOf(row.get("TOTAL_SUBCRIBED_SCENARIO")));
                    if(nonReg == 0) {
                        continue;
                    }
                    
                    if (hasAdditionalBasePack) {
                        additionalBasePackCount++;
                    } else {
                        nonAdditionalBasePackCount++;
                    }
                    logger.info(String.format(
                            "getBasePackRecords Meter usage Basepack additionabasepackflag: %s, customerId: %s, tenantId: %s",
                            hasAdditionalBasePack, customerId, tenantId));
                }

                Long nonbasepackFinalValue = nonAdditionalBasePackCount > 0 ? 1L : 0L;
                finalValue = additionalBasePackCount > 0 ? nonbasepackFinalValue + additionalBasePackCount: nonbasepackFinalValue; 
                logger.info(String.format(
                            "getBasePackRecords Meter usage Basepack nonbasepackFinalValue: %s, nonAdditionalBasePackCount: %s, additionalBasePackCount: %s",
                            nonbasepackFinalValue, nonAdditionalBasePackCount, additionalBasePackCount));

            }

            Record rcd = createRecord("", tenantId, measurementObjectId, finalValue);
            recordList.add(rcd);

            logger.info(String.format("getBasePackRecords Meter usage Basepack final value. additionabasepackcount: %d, nonaddtionalbasepackcount: %d, finalcount: %d, tenantId: %s",
                additionalBasePackCount, nonAdditionalBasePackCount, finalValue, tenantId));
        }

        return recordList;
    }


    private Record createRecord(String customerId,String crmTenantId, String measureId, Long measureValue) {
        String timestamp = ZonedDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        String aggregationType = properties.getAggregationType();

        return createRecord(customerId,crmTenantId, measureId, measureValue, timestamp,aggregationType);
    }

    private Record createRecord(String customerId,String crmTenantId, String measureId, Long measureValue, String executionTimeStamp,String aggregationType) {
        return createRecordPayload(customerId,crmTenantId, measureId, measureValue, executionTimeStamp,aggregationType);
    }

    private Record createRecordPayload(String customerId,String crmTenantId, String measureId, Long measureValue, String timestamp,String aggregationType) {
        String uuid = UUID.randomUUID().toString();
        OriginServiceInstance originServiceInstance = new OriginServiceInstance();
        originServiceInstance.id = crmTenantId;
        originServiceInstance.type = properties.getInstanceType();
        Consumer consumer = new Consumer();
        consumer.originServiceInstance = originServiceInstance;
        

        Measure measure = new Measure();
        measure.id = measureId;
        measure.value = measureValue;

        GudfContext context = new GudfContext();
        context.aggregationType = aggregationType;

        Record rcd = new Record();
        rcd.customerId = customerId;
        rcd.id = uuid;
        rcd.timestamp = timestamp;
        rcd.consumer = consumer;
        rcd.measure = measure;
        rcd.gudfContext = context;

        return rcd;
    }

    private String dateToTimestampUtil(LocalDate date) {
        ZonedDateTime zonedDateTime = ZonedDateTime.from(date.atTime(12, 0, 0, 0).atZone(ZoneId.of("UTC")));
        return zonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
    }

    private String dateToTimestampUtil(String date) {
        return date + "T12:00:00.000Z";
    }

    class Record {
        String id;
        transient String customerId;
        String timestamp;
        Consumer consumer;
        Measure measure;
        GudfContext gudfContext;

        public void setMeasurementObjectId(String measurementObjectId) {
            this.measure.setId(measurementObjectId);
        }
        public void sumValue(Record ortherRecord) {
            this.measure.setValue(this.measure.getValue()+ortherRecord.measure.getValue());
        }
        public String getTenantId() {
            return this.consumer.originServiceInstance.id;
        }

    }

    class Consumer {
        OriginServiceInstance originServiceInstance;
    }

    class OriginServiceInstance {
        String id;
        String type;
    }

    class Measure {
        String id;
        Long value;

        public void setValue(Long newValue) {
            this.value = newValue;
        }

        public void setId(String newId) {
            this.id = newId;
        }

        public Long getValue() {
            return this.value;
        }

        public String getId() {
            return this.id;
        }
    }

    class GudfContext {
        String aggregationType;
    }
}
