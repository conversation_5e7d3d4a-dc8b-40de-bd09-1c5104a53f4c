import org.jenkinsci.plugins.pipeline.modeldefinition.Utils
void call(Map params) {
  echo "Start - Extension for stage: ${params.stageName}"

  //execute original stage as defined in the template
  if(env.BRANCH_NAME.startsWith('production') || env.BRANCH_NAME.startsWith('staging') || env.BRANCH_NAME.startsWith('hyperspace')) {
    params.originalStage()
  } else {
    echo "Skipped: Promote only done for production & staging branch!"
    Utils.markStageSkippedForConditional(params.stageName)
  }

  echo "End - Extension for stage: ${params.stageName}"
}
return this
