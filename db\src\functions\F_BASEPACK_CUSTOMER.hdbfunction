FUNCTION "F_BASEPACK_CUSTOMER"() RETURNS TABLE (
    "CUSTOMER_ID" NVARCHAR(255),
    "TOTAL_SUBCRIBED_SCENARIO" INTEGER,
    "TOTAL_DISCONN_CURRENT_YEAR" INTEGER,
    "TENANT_ID" NVARCHAR(255),
    "ADDITONAL_BASE_PACK" BOOLEAN
) LANGUAGE SQLSCRIPT SQL SECURITY INVOKER AS BEGIN
    RETURN
    SELECT  
        CUSTOMER_ID,
        CASE 
            WHEN TOTAL_REG_SCENARIO > 0 AND TOTAL_NONREG_SCENARIO > 0 THEN 1 
            WHEN TOTAL_REG_SCENARIO = 0 AND TOTAL_NONREG_SCENARIO > 0 THEN 1
            WHEN TOTAL_REG_SCENARIO > 0 AND TOTAL_NONREG_SCENARIO = 0 THEN 0
            ELSE 0 
        END AS TOTAL_SUBCRIBED_SCENARIO,
        TOTAL_DISCONN_CURRENT_YEAR,
        TENANT_ID,
        ADDITONAL_BASE_PACK
    FROM (
        SELECT 
            org."ID" AS "CUSTOMER_ID",
            COUNT(CASE WHEN orgscen.ISREMOVED = 0 AND orgscen.SCENARIO_ID NOT IN (2,3,7,9,10,12,13,14,16,18,20) THEN orgscen.ORG_ID END) AS "TOTAL_NONREG_SCENARIO",
            COUNT(CASE WHEN orgscen.ISREMOVED = 0 AND orgscen.SCENARIO_ID IN (2,3,7,9,10,12,13,14,16,18,20) THEN orgscen.ORG_ID END) AS "TOTAL_REG_SCENARIO",
            0 AS "TOTAL_DISCONN_CURRENT_YEAR",
            org.TENANTID AS "TENANT_ID",
            org.ADDITIONALBASEPACK AS "ADDITONAL_BASE_PACK"
        FROM 
            ICH_USAGE_MEASUREMENT_ORGANIZATIONSCENARIO orgscen
        LEFT JOIN 
            ICH_USAGE_MEASUREMENT_ORGANIZATION AS org ON org.id = orgscen.ORG_ID
        WHERE 
            org.TYPE_ID = 1 
            AND org.STATUS_ID = 1 
        GROUP BY 
            org."ID", org.TENANTID, org.ADDITIONALBASEPACK
    );
END