import com.sap.piper.BashUtils

void call(Map params) {
    //access stage name
    echo "Start - Extension for stage: ${params.stageName}"

    //access config
    echo "Current stage config: ${params.config}"

    echo "gitBranch: ${params.script.commonPipelineEnvironment.gitBranch}"

    params.originalStage()

    if (env.BRANCH_NAME.equals('develop') || env.BRANCH_NAME.startsWith('release') || env.BRANCH_NAME.startsWith('hotfix')) {
        
        stage("Credential Digger Scan"){
            echo "==== Credential Digger Scan ===="
            script{
                catchError(buildResult: 'SUCCESS', stageResult: 'SUCCESS'){
                    def gitHost = "https://github.wdf.sap.corp/"

                    credentialdiggerScan script: this, repository: gitHost + params.script.commonPipelineEnvironment.githubOrg + "/" + params.script.commonPipelineEnvironment.githubRepo, snapshot: params.script.commonPipelineEnvironment.gitBranch
                    
                    echo "==== Finished Credential Digger Scan ===="
                }
            }
        }
    }

    //access overall pipeline script object
    echo "Branch: ${params.script.commonPipelineEnvironment.gitBranch}"

    echo "End - Extension for stage: ${params.stageName}"
}

return this
