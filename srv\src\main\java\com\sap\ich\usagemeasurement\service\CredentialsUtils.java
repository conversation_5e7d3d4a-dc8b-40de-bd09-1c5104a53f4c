package com.sap.ich.usagemeasurement.service;


import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cms.CMSException;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.util.Store;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Collection;

public class CredentialsUtils {

    private CredentialsUtils() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static PrivateKey convertPKCS8ToPKCS1(String pkcs8Key) throws Exception {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

        PEMParser pemParser = new PEMParser(new StringReader(pkcs8Key));
        final PrivateKeyInfo keyInfo = (PrivateKeyInfo) pemParser.readObject();
        final byte[] encoded = keyInfo.getEncoded();
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encoded));
    }

    public static X509Certificate[] convertToX509Certificate(String pkcs7Cert) throws IOException, CertificateException, CMSException {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

        PemObject pemObject;
        try (PemReader pemReader = new PemReader(new StringReader(pkcs7Cert))) {
            pemObject = pemReader.readPemObject();
        }

        ByteArrayInputStream inputStream = new ByteArrayInputStream(pemObject.getContent());
        CMSSignedData signedData = new CMSSignedData(inputStream);
        Store<X509CertificateHolder> certificates = signedData.getCertificates();
        Collection<X509CertificateHolder> certCollection = certificates.getMatches(null);

        if (certCollection.isEmpty()) {
            throw new CertificateException("No certificates found in the PKCS7 data.");
        }

        X509Certificate[] x509Certificates = new X509Certificate[certCollection.size()];
        int i = 0;
        for (X509CertificateHolder certificateHolder : certCollection) {
            x509Certificates[i] = new JcaX509CertificateConverter().getCertificate(certificateHolder);
            i++;
        }

        return x509Certificates;
    }
}