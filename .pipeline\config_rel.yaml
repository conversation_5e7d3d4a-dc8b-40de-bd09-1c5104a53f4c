general:
  buildTool: 'mta'
  productiveBranch: 'production|staging|hyperspace'
  gitHttpsCredentialsId: 'ICH_tkn_GitHub'
  vaultAppRoleTokenCredentialsId: 'vault-approle-role-id-2614-33723'
  vaultAppRoleSecretTokenCredentialsId: 'vault-approle-secret-id-accessor-2614-33723'
  vaultPath: 'piper/PIPELINE-GROUP-2614'
  vaultBasePath: 'piper/PIPELINE-GROUP-2614'
  vaultPipelineName: 'PIPELINE-33723'
  vaultServerUrl: 'https://vault.tools.sap'
  vaultNamespace: 'ies/hyperspace/pipelines'
  verbose: true
stages:
  Central Build:
    hadolintExecute: false
  Acceptance:
    verbose: true
    cfApiEndpoint: https://api.cf.eu10-004.hana.ondemand.com
    cfOrg: ich-qa-new-org
    cfSpace: portal
    cfCredentialsId: 'ICH_pwd_Cloud_Platform'
    mtaDeployParameters: -f -version-rule ALL
    testServerUrl: www.google.com
  Confirm:
    manualConfirmation: false
    manualConfirmationTimeout: 0.001
steps:
  artifactPrepareVersion:
    gitHttpsCredentialVaultSecretName: 'GROUP-SECRETS/github'
    versioningType: 'library'
  mtaBuild:
    mtaBuildTool: cloudMbt
  executeBuild:
    xMakeNovaCredentialsId: 'hyperspace-xmake-p2003178105'
  sonarExecuteScan:
    serverUrl: 'https://sonar.tools.sap'
    sonarTokenCredentialsId: 'hyperspace-sonar-c5182688'
    sonarVaultSecretName: 'GROUP-SECRETS/sonar'
    projectKey: 'Staging_ICH-portal-Usage-Measurement-Service'
    projectName: 'Staging_portal-Usage-Measurement-Service'
  sapCumulusUpload:
    pipelineId: '1b5f1124-c8b6-40e3-887a-ad460e0315c8'
    cumulusFileCredentialsId: 'hyperspace-cumulusupload-2614'
