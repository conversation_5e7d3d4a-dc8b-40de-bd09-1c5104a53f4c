## Generated mta.yaml based on template version 0.4.0
## appName = organization-management
## language=java; multitenant=false
## approuter=
_schema-version: "3.1"
ID: ich-usage-measurement-srv
version: 2420.0.0
description: "Usage Measurement Service"
parameters:
  enable-parallel-deployments: true

modules:
 # --------------------- SERVER MODULE ------------------------
 - name: por-usage-measurement
 # ------------------------------------------------------------
   type: java
   path: srv
   parameters:
     buildpack: sap_java_buildpack
     stack: cflinuxfs4
   build-parameters:
     builder: custom
     commands: 
       - mvn clean package -P cdsdk-global -DskipTests=true
     build-result: target/*.jar
   properties:
     SPRING_PROFILES_ACTIVE: cloud
     JBP_CONFIG_COMPONENTS: "jres: ['com.sap.xs.java.buildpack.jre.SAPMachineJRE']"
     JBP_CONFIG_SAP_MACHINE_JRE: '{ use_offline_repository: false, version: 17.+ }'
     BILLING_MODE: "metering$1"
     INSTANCE_TYPE: "ICH"
     AGGREGATION_TYPE: "MO"
     BP_VIRTAUL_TENANT_ID: 9999999
   requires:
    # Resources extracted from CAP configuration
     - name: por-usage-measurement-db
     - name: por-usage-measurement-uaa
     - name: por-dest-common-horizontal
     - name: por-unified-metering
     - name: por-usage-jobscheduler
     - name: por-logging-common-horizontal
   provides:
    - name: srv-api      # required by consumers of CAP services (e.g. approuter)
      properties:
        srv-url: ${default-url}

 # -------------------- SIDECAR MODULE ------------------------
 - name: usage-management-db-deployer
 # ------------------------------------------------------------
   type: hdb
   path: db  
   build-parameters:
     ignore: ["node_modules", "package-lock.json"]
   parameters:
      app-name: por-usage-measurement-db
      memory: 256M
      disk-quota: 256M
      stack: cflinuxfs4
   requires:
    # 'hana' and 'xsuaa' resources extracted from CAP configuration
    - name: por-usage-measurement-db
      properties: 
          TARGET_CONTAINER: por-usage-measurement-db   
    - name: por-db-master-data
      group: SERVICE_REPLACEMENTS
      properties:
          key: master-db-hdi
          service: ~{master-db-hdi} 
    - name: por-db-user-mgmt
      group: SERVICE_REPLACEMENTS
      properties:
          key: usermgmt-db-hdi
          service: ~{usermgmt-db-hdi} 
    - name: por-organization-mgmt-db
      group: SERVICE_REPLACEMENTS
      properties:
        key: por-organization-mgmt-db-hdi
        service: ~{por-organization-mgmt-db-hdi}   
    - name: por-db-common
      group: SERVICE_REPLACEMENTS
      properties:
          key: por-db-common-hdi
          service: ~{por-db-common-hdi}     

resources:
 # services extracted from CAP configuration
 # 'service-plan' can be configured via 'cds.requires.<name>.vcap.plan'
# ------------------------------------------------------------
 - name: por-usage-measurement-db
# ------------------------------------------------------------
   type: com.sap.xs.hdi-container
   parameters:
     service: hana  # or 'hanatrial' on trial landscapes
     service-plan: hdi-shared
   properties:
     hdi-service-name: ${service-name}
 - name: por-organization-mgmt-db
   type: org.cloudfoundry.existing-service
   parameters:
     service: hana
     service-name: por-organization-mgmt-db  
   properties:
     por-organization-mgmt-db-hdi: ${service-name} 
 - name: por-db-master-data
   type: org.cloudfoundry.existing-service
   parameters:
     service: hana
     service-name: por-db-master-data  
   properties:
      master-db-hdi: ${service-name}
 - name: por-db-user-mgmt
   type: org.cloudfoundry.existing-service
   parameters:
     service: hana
     service-name: por-db-user-mgmt  
   properties:
      usermgmt-db-hdi: ${service-name}
 - name: por-db-common
   type: org.cloudfoundry.existing-service
   parameters:
     service: hana
     service-name: por-db-common  
   properties:
      por-db-common-hdi: ${service-name}
 - name: por-unified-metering
   type: org.cloudfoundry.managed-service
   parameters:
     service: metering-service
     service-plan: unified-metering
     config:
       apiVersion: metering.cloud.sap.com/v1
       type: Account
       metadata:   
        name: por-unified-metering
        path: /sap/dsc/d2o-deliver/ich/ich-life-science/ich-resource-group
       um_account:
        additionalDataStreams:
          - btp
 - name: por-usage-jobscheduler
   type: org.cloudfoundry.managed-service
   parameters:
     service: jobscheduler
     service-plan: standard
     config:
       enable-xsuaa-support: true
 - name: por-logging-common-horizontal
   type: org.cloudfoundry.managed-service
   parameters:
     service: cloud-logging
     service-plan: standard
 - name: por-dest-common-horizontal
   type: org.cloudfoundry.managed-service
   parameters:
    service: destination
    service-plan: lite   
# ------------------------------------------------------------
 - name: por-usage-measurement-uaa
# ------------------------------------------------------------
   type: org.cloudfoundry.managed-service
   parameters:
     service: xsuaa
     service-plan: application 
     path: ./xs-security.json 
     config:
       xsappname: por-usage-measurement-${space}    #  name + space dependency
       tenant-mode: dedicated
