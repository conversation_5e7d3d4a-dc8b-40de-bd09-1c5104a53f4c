FUNCTION "F_USER_SCENARIO_SUBB_YEAR"() RETURNS TABLE (
    "CUSTOMER_ID" NVARCHAR(255),
    "TOTAL_SUBCRIBED_SCENARIO" INTEGER,
    "TOTAL_DISCONN_CURRENT_YEAR" INTEGER,
    "TENANT_ID" NVARCHAR(255)
) LANGUAGE SQLSCRIPT SQL SECURITY INVOKER AS BEGIN RETURN
    
    WITH combined_results AS (
        -- US Scenario - Trace and Exception
        SELECT
            org.ID AS CUSTOMER_ID,
            COUNT(CASE 
                WHEN orgscen.ISREMOVED = 0 AND orgscen.SCENARIO_ID IN (15, 19) 
                THEN orgscen.ORG_ID 
            END) AS TOTAL_SUBSCRIBED_SCENARIO,
            0 AS TOTAL_DISCONN_CURRENT_YEAR,
            org.TENANTID AS TENANT_ID
        FROM 
            ICH_USAGE_MEASUREMENT_ORGANIZATIONSCENARIO orgscen
        LEFT JOIN 
            ICH_USAGE_MEASUREMENT_ORGANIZATION org ON org.ID = orgscen.ORG_ID
        WHERE 
            org.TYPE_ID = 1
            AND org.STATUS_ID = 1
        GROUP BY 
            org.ID, 
            org.TENANTID

        UNION ALL

        -- No US Russia MDLP OMS, Other RegReporting
        SELECT
            inv.inviterpnid AS CUSTOMER_ID,
            COUNT(CASE 
                WHEN inv.scenario_id = 13 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 2 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 3 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 7 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 9 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 10 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 12 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 14 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 16 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 18 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 20 AND inv."INVITEECONNSTATUS_ID" = 3 AND inv.isdeleted = FALSE
                THEN inv.inviterpnid 
            END) AS TOTAL_SUBSCRIBED_SCENARIO,
            COUNT(CASE 
                WHEN inv.scenario_id = 13 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 2 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 3 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 7 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 9 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 10 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 12 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 14 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 16 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END)
            + COUNT(CASE 
                WHEN inv.scenario_id = 18 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END) 
            + COUNT(CASE 
                WHEN inv.scenario_id = 20 AND inv.isdeleted = TRUE AND YEAR(inv.modifiedat) = YEAR(CURRENT_DATE)
                THEN inv.inviterpnid 
            END) AS TOTAL_DISCONN_CURRENT_YEAR,
            org.TENANTID AS TENANT_ID
        FROM 
            ICH_USAGE_MEASUREMENT_INVITATION inv
        JOIN
            ICH_USAGE_MEASUREMENT_ORGANIZATION org ON inv.inviterpnid = org.ID
        WHERE 
            org.TYPE_ID = 1
            AND org.STATUS_ID = 1
        GROUP BY 
            inv.inviterpnid, 
            org.TENANTID
    )

    -- Aggregate
    SELECT 
        CUSTOMER_ID,
        SUM(TOTAL_SUBSCRIBED_SCENARIO) AS TOTAL_SUBCRIBED_SCENARIO,
        0 AS TOTAL_DISCONN_CURRENT_YEAR,
        TENANT_ID
    FROM
        combined_results
    GROUP BY 
        CUSTOMER_ID,
        TENANT_ID;

END