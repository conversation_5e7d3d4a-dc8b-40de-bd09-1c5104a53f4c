#!/usr/bin/env groovy
@Library(['piper-lib', 'piper-lib-os']) _

properties([
    parameters([
        choice(name: 'BRANCH', choices: ['production', 'staging','hyperspace'], description: 'Select the branch to deploy'),
	choice(name: 'ENVIRONMENT', choices: ['QA', 'QA Validation', 'Validation', 'Staging', 'Production'], description: 'Select the environment to deploy to')
    ])
])

try {
    stage('Central Build') {
        lock(resource: "${env.JOB_NAME}/10", inversePrecedence: true) {
            milestone 10
            node {
                deleteDir()
                checkout scm
                setupPipelineEnvironment script: this
                durationMeasure (script: this, measurementName: 'build_duration') {          
                }
            }
        }
    }

    stage('Deploy') {
        lock(resource: "${env.JOB_NAME}/10", inversePrecedence: true) {
            milestone 20
            node {
                deleteDir()
                durationMeasure(script: this, measurementName: 'deploy_duration') {
                    checkout scm
                    downloadArtifactsFromNexus script: this, disableLegacyNaming: true , nexusUrl: 'https://int.repositories.cloud.sap/', promoteEndpoint : 'artifactory/', promoteRepository: 'build-milestones/', fromStaging: false, xMakeBuildQuality:'Milestone', artifactType: 'mta', buildTool: 'mta', verbose: true
                    if (params.ENVIRONMENT == 'QA') {
                        echo 'Deploying to QA Environment'
                        currentBuild.description = "Deploying to QA Environment"
                        cloudFoundryDeploy script: this, cfOrg: 'ich-qa-new-org', deployType: 'blue-green', cfSpace: 'portal', deployTool: 'mtaDeployPlugin', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu10-004.hana.ondemand.com', mtaDeployParameters: "--delete-services"
                    } else if (params.ENVIRONMENT == 'QA Validation') {
                        echo 'Deploying to QA Validation Environment'
                        currentBuild.description = "Deploying to QA Validation Environment"
                        cloudFoundryDeploy script: this, cfOrg: 'ich-qa-validation', deployType: 'blue-green', cfSpace: 'portal', deployTool: 'mtaDeployPlugin', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu10-004.hana.ondemand.com', mtaDeployParameters: "--delete-services"
                    } else if (params.ENVIRONMENT == 'Validation') {
                        echo 'Deploying to Validation Environment'
                        currentBuild.description = "Deploying to Validation Environment"
                        cloudFoundryDeploy script: this, cfOrg: 'ich-staging-validation', deployType: 'blue-green', cfSpace: 'portal', deployTool: 'mtaDeployPlugin', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu10-004.hana.ondemand.com', mtaDeployParameters: "--delete-services"
                    }
			else if (params.ENVIRONMENT == 'Staging') {
                        echo 'Deploying to Staging Environment'
                        currentBuild.description = "Deploying to Staging Environment"
                        cloudFoundryDeploy script: this, cfOrg: 'ich-staging-org', deployType: 'blue-green', cfSpace: 'portal', deployTool: 'mtaDeployPlugin', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu10.hana.ondemand.com', mtaDeployParameters: "--delete-services"
                    }		
					else if (params.ENVIRONMENT == 'Production') {
                        echo 'Deploying to Production Environment'
                        currentBuild.description = "Deploying to Production Environment"
                        cloudFoundryDeploy script: this, cfOrg: 'ich-prod-org', deployType: 'blue-green', cfSpace: 'portal', deployTool: 'mtaDeployPlugin', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu10.hana.ondemand.com', mtaDeployParameters: "--delete-services"
                    }  
                }
            }
        }
    }

} catch (Throwable err) { // catch all exceptions
    globalPipelineEnvironment.addError(this, err)
    throw err
} finally {
    node{
        mailSendNotification script: this
        cleanWs()
    }
}
