@clientid = 
@clientsecret = 

@token = {{tokenRequest.response.body.$.access_token}}

@apphost =  https://ich-dev-org-portal-por-usage-measurement.cfapps.eu12.hana.ondemand.com

@tokenhost = https://ich-dev-eu12.authentication.eu12.hana.ondemand.com/oauth/token

# @name tokenRequest
GET {{tokenhost}}/oauth/token?grant_type=client_credentials&response_type=token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {{clientid}}:{{clientsecret}}

###
POST {{apphost}}/api/v1/sendscpcconnusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendticonnusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getticonnusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendregreportusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendbasepackusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getbasepackusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendstametrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getstametrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendtiserialmetrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/gettiserialmetrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendmmusage
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "startDate": "2024-07-01",
    "endDate" : "2024-08-01"
}

###
POST {{apphost}}/api/v1//sendActiveConnectionUsage
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "startDate": "2021-08-01",
    "endDate" : "2023-12-11"
}

###
POST {{apphost}}/api/v1/sendcustomerregcollab?date=2025-02-16
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getcustomerregcollab
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendcompletekeymetrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getcompletekeymetrics
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendcustomerusoption/MO
Content-Type: application/json
x-job-mode: false
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/sendcustomerusoption/DA
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/retrysendcustomerusoption?date=2025-04-01
Content-Type: application/json
Authorization: Bearer {{token}}

###
GET {{apphost}}/api/v1/getusuage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST {{apphost}}/api/v1/recordusage
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "pnid": "PNNOVARTIS20160811",
    "measuringObject":"ICH_UM_BIS_ACCESS",
    "value": 1
}

###
POST {{apphost}}/api/v1/aggregatebisdailyusage
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "date": "2024-09-30"
}

###
POST {{apphost}}/api/v1/sendbisusage
Content-Type: application/json
Authorization: Bearer {{token}}

###
POST https://ich-dev-org-portal-por-usage-measurement.cfapps.eu12.hana.ondemand.com/api/v1/sendusagedata
Content-Type: application/json
Authorization: Bearer {{token}}
 
[{
        "id": "eaa7f35f-40f1-470a-b4a9-6564a127c459",
        "timestamp": "2025-01-30T17:00:00.000Z",
        "consumer": {
            "originServiceInstance": {
                "id": "741197788",
                "type": "ICH"
            }
        },
        "measure": {
            "id": "ICH_UM_CUSTOMER_REG_COLL_CONNECTION",
            "value": 45
        },
        "gudfContext": {
            "aggregationType": "MO"
        }
}]