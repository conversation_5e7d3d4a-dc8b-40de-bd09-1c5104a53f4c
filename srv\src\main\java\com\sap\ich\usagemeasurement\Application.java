package com.sap.ich.usagemeasurement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.sap.ich.usagemeasurement.configuration.VcapProperties;

import static com.sap.ich.usagemeasurement.service.CredentialsUtils.convertToX509Certificate;

import java.security.PrivateKey;
import javax.net.ssl.SSLContext;
import java.security.KeyStore;
import java.security.cert.X509Certificate;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.BasicHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.ssl.TLS;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.sap.ich.usagemeasurement.service.CredentialsUtils.convertPKCS8ToPKCS1;
@SpringBootApplication
@EnableAsync
public class Application {

	@Autowired
    private VcapProperties properties;

    private final Logger logger = LoggerFactory.getLogger(getClass());

	@Profile("cloud")
    @Bean
    public RestTemplate restOperations() throws Exception {

        PrivateKey key = convertPKCS8ToPKCS1(properties.getKey());
        logger.info("certloadevent -  {}, from account: {}", properties.getCertificate(), properties.getKey());
        X509Certificate[] certificates = convertToX509Certificate(properties.getCertificate());

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(null, null);
        keyStore.setKeyEntry("alias", key, null, certificates);

        SSLContext sslContext = SSLContextBuilder.create()
            .loadKeyMaterial(keyStore, null) // Load key material// Load trust material, modify as needed
            .build();

        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);

        HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create()
                .setSSLSocketFactory(sslsf)
                .build();

        final CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm)
        .build();

        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        return new RestTemplate(requestFactory);
    }
	
	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

    @Profile("test")
    @Bean
    public RestTemplate restOperationTest() throws Exception {
        return new RestTemplate();
    }

    
    
}
