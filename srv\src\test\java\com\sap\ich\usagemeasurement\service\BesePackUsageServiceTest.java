package com.sap.ich.usagemeasurement.service;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.sap.cds.Result;
import com.sap.cds.Row;
import com.sap.cds.ql.Select;
import com.sap.cds.services.persistence.PersistenceService;
import com.sap.ich.usagemeasurement.Application;
import com.sap.ich.usagemeasurement.configuration.VcapProperties;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class BesePackUsageServiceTest {

    @InjectMocks
    private CustomerUsageService customerUsageService;

    @Mock
    private PersistenceService db;

    @Mock
    private VcapProperties properties;

    @Mock
    private Result result;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(properties.getAggregationType()).thenReturn("SUM");
        when(properties.getInstanceType()).thenReturn("test-instance");
    }

    /**
     * Test case 1: Single organization with basepack true, final value should be 1
     */
    @Test
    public void testGetBasePackRecords_SingleOrganization_BasePackTrue_FinalValue1() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_SingleOrgBasePackTrue();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(1L), finalValue, "Final value should be 1");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    /**
     * Test case 2: Single organization with basepack false, final value should be 1
     */
    @Test
    public void testGetBasePackRecords_SingleOrganization_BasePackFalse_FinalValue1() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_SingleOrgBasePackFalse();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(1L), finalValue, "Final value should be 1");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    /**
     * Test case 3: Multiple organizations - cus1: basepack false, cus2: basepack true, final value should be 2
     */
    @Test
    public void testGetBasePackRecords_MultipleOrganizations_MixedBasePack_FinalValue2() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_MultipleOrgsMixed();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(2L), finalValue, "Final value should be 2");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    /**
     * Test case 4: Multiple organizations - cus1: basepack true, cus2: basepack true, final value should be 2
     */
    @Test
    public void testGetBasePackRecords_MultipleOrganizations_BothBasePackTrue_FinalValue2() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_MultipleOrgsBothTrue();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(2L), finalValue, "Final value should be 2");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    /**
     * Test case 5: Multiple organizations - cus1: basepack false, cus2: basepack true, cus3: basepack false, final value should be 2
     */
    @Test
    public void testGetBasePackRecords_MultipleOrganizations_ThreeOrgsMixed_FinalValue2() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_ThreeOrgsMixed();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(2L), finalValue, "Final value should be 2");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    /**
     * Test case 6: Multiple organizations - cus1: basepack true, cus2: basepack true, cus3: basepack true, final value should be 3
     */
    @Test
    public void testGetBasePackRecords_MultipleOrganizations_AllBasePackTrue_FinalValue3() throws Exception {
        // Arrange
        List<Row> mockRows = createMockRows_ThreeOrgsAllTrue();
        when(db.run(any(Select.class))).thenReturn(result);
        when(result.list()).thenReturn(mockRows);

        // Act
        List<?> records = invokeGetBasePackRecords("TEST_MEASUREMENT_ID", "TEST_RUNLOG_UID");

        // Assert
        assertNotNull(records, "Records should not be null");
        assertEquals(1, records.size(), "Should have exactly 1 record");

        Object record = records.get(0);
        Long finalValue = getFinalValueFromRecord(record);
        assertEquals(Long.valueOf(3L), finalValue, "Final value should be 3");

        String tenantId = getTenantIdFromRecord(record);
        assertEquals("tenant1", tenantId, "Tenant ID should match");
    }

    // Helper methods for invoking private method and extracting values
    @SuppressWarnings("unchecked")
    private List<?> invokeGetBasePackRecords(String measurementObjectId, String runlogUid) throws Exception {
        Method method = CustomerUsageService.class.getDeclaredMethod("getBasePackRecords", String.class, String.class);
        method.setAccessible(true);
        return (List<?>) method.invoke(customerUsageService, measurementObjectId, runlogUid);
    }

    private Long getFinalValueFromRecord(Object record) throws Exception {
        java.lang.reflect.Field measureField = record.getClass().getDeclaredField("measure");
        measureField.setAccessible(true);
        Object measure = measureField.get(record);

        java.lang.reflect.Field valueField = measure.getClass().getDeclaredField("value");
        valueField.setAccessible(true);
        return (Long) valueField.get(measure);
    }

    private String getTenantIdFromRecord(Object record) throws Exception {
        java.lang.reflect.Field consumerField = record.getClass().getDeclaredField("consumer");
        consumerField.setAccessible(true);
        Object consumer = consumerField.get(record);

        java.lang.reflect.Field originServiceInstanceField = consumer.getClass().getDeclaredField("originServiceInstance");
        originServiceInstanceField.setAccessible(true);
        Object originServiceInstance = originServiceInstanceField.get(consumer);

        java.lang.reflect.Field idField = originServiceInstance.getClass().getDeclaredField("id");
        idField.setAccessible(true);
        return (String) idField.get(originServiceInstance);
    }

    // Mock data creation methods
    private List<Row> createMockRows_SingleOrgBasePackTrue() {
        List<Row> rows = new ArrayList<>();
        Row row = createMockRow("customer1", "tenant1", 1, true);
        rows.add(row);
        return rows;
    }

    private List<Row> createMockRows_SingleOrgBasePackFalse() {
        List<Row> rows = new ArrayList<>();
        Row row = createMockRow("customer1", "tenant1", 1, false);
        rows.add(row);
        return rows;
    }

    private List<Row> createMockRows_MultipleOrgsMixed() {
        List<Row> rows = new ArrayList<>();
        Row row1 = createMockRow("customer1", "tenant1", 1, false);
        Row row2 = createMockRow("customer2", "tenant1", 1, true);
        rows.add(row1);
        rows.add(row2);
        return rows;
    }

    private List<Row> createMockRows_MultipleOrgsBothTrue() {
        List<Row> rows = new ArrayList<>();
        Row row1 = createMockRow("customer1", "tenant1", 1, true);
        Row row2 = createMockRow("customer2", "tenant1", 1, true);
        rows.add(row1);
        rows.add(row2);
        return rows;
    }

    private List<Row> createMockRows_ThreeOrgsMixed() {
        List<Row> rows = new ArrayList<>();
        Row row1 = createMockRow("customer1", "tenant1", 1, false);
        Row row2 = createMockRow("customer2", "tenant1", 1, true);
        Row row3 = createMockRow("customer3", "tenant1", 1, false);
        rows.add(row1);
        rows.add(row2);
        rows.add(row3);
        return rows;
    }

    private List<Row> createMockRows_ThreeOrgsAllTrue() {
        List<Row> rows = new ArrayList<>();
        Row row1 = createMockRow("customer1", "tenant1", 1, true);
        Row row2 = createMockRow("customer2", "tenant1", 1, true);
        Row row3 = createMockRow("customer3", "tenant1", 1, true);
        rows.add(row1);
        rows.add(row2);
        rows.add(row3);
        return rows;
    }

    private Row createMockRow(String customerId, String tenantId, int totalSubscribedScenario, boolean additionalBasePack) {
        Row row = mock(Row.class);
        when(row.get("CUSTOMER_ID")).thenReturn(customerId);
        when(row.get("TENANT_ID")).thenReturn(tenantId);
        when(row.get("TOTAL_SUBCRIBED_SCENARIO")).thenReturn(totalSubscribedScenario);
        when(row.get("ADDITONAL_BASE_PACK")).thenReturn(additionalBasePack);
        return row;
    }
}